/**
 * صفحة صلاحيات المستخدمين - تصميم بطاقات حديث
 */

// إعدادات API
const API_URL = 'http://localhost:5500/api';

// متغيرات عامة
let currentUser = null;
let originalPermissions = {};
let permissionsStructure = {};

// تعريف هيكل الصلاحيات مع الأيقونات والأوصاف
const PERMISSIONS_STRUCTURE = {
    general: {
        title: 'الصلاحيات العامة',
        icon: 'fas fa-cogs',
        permissions: {
            can_view: { label: 'عرض البيانات', description: 'إمكانية عرض جميع البيانات في النظام' },
            can_add: { label: 'إضافة البيانات', description: 'إمكانية إضافة بيانات جديدة' },
            can_edit: { label: 'تعديل البيانات', description: 'إمكانية تعديل البيانات الموجودة' },
            can_delete: { label: 'حذف البيانات', description: 'إمكانية حذف البيانات' }
        }
    },
    employees: {
        title: 'إدارة الموظفين',
        icon: 'fas fa-users',
        permissions: {
            view_employees: { label: 'عرض الموظفين', description: 'الوصول لصفحة الموظفين وعرض بياناتهم' },
            add_employees: { label: 'إضافة موظف', description: 'إمكانية إضافة موظفين جدد' },
            edit_employees: { label: 'تعديل الموظف', description: 'إمكانية تعديل بيانات الموظفين' },
            delete_employees: { label: 'حذف الموظف', description: 'إمكانية حذف موظف واحد' },
            export_employees: { label: 'تصدير الموظفين', description: 'تصدير بيانات الموظفين إلى Excel' },
            calculate_leave_balance: { label: 'حساب رصيد الإجازات', description: 'حساب رصيد الإجازات لجميع الموظفين' },
            calculate_used_leave: { label: 'حساب الإجازات المستخدمة', description: 'حساب الإجازات المستخدمة لجميع الموظفين' },
            delete_all_employees: { label: 'حذف جميع الموظفين', description: 'حذف جميع الموظفين من النظام (عملية خطيرة)' }
        }
    },
    vacations: {
        title: 'إدارة الإجازات',
        icon: 'fas fa-calendar-alt',
        permissions: {
            view_vacations: { label: 'عرض الإجازات', description: 'الوصول لصفحة الإجازات' },
            add_vacation: { label: 'إضافة إجازة', description: 'إمكانية إضافة إجازات جديدة' },
            edit_vacation: { label: 'تعديل الإجازة', description: 'إمكانية تعديل بيانات الإجازات' },
            delete_vacation: { label: 'حذف الإجازة', description: 'إمكانية حذف الإجازات' },
            view_vacations_list: { label: 'قائمة الإجازات', description: 'عرض قائمة جميع الإجازات' },
            view_vacation_reports: { label: 'تقارير الإجازات', description: 'عرض وتصدير تقارير الإجازات' }
        }
    },
    training: {
        title: 'إدارة التدريب',
        icon: 'fas fa-graduation-cap',
        permissions: {
            view_training: { label: 'عرض التدريب', description: 'الوصول لصفحة التدريب' },
            add_training: { label: 'إضافة دورة تدريبية', description: 'إمكانية إضافة دورات تدريبية جديدة' },
            edit_training: { label: 'تعديل التدريب', description: 'إمكانية تعديل بيانات التدريب' },
            delete_training: { label: 'حذف التدريب', description: 'إمكانية حذف الدورات التدريبية' },
            view_training_reports: { label: 'تقارير التدريب', description: 'عرض وتصدير تقارير التدريب' }
        }
    },
    evaluations: {
        title: 'إدارة التقييمات',
        icon: 'fas fa-star',
        permissions: {
            view_evaluation: { label: 'عرض التقييمات', description: 'الوصول لصفحة التقييمات' },
            add_evaluation: { label: 'إضافة تقييم', description: 'إمكانية إضافة تقييمات جديدة' },
            edit_evaluation: { label: 'تعديل التقييم', description: 'إمكانية تعديل التقييمات' },
            delete_evaluation: { label: 'حذف التقييم', description: 'إمكانية حذف التقييمات' },
            view_evaluation_reports: { label: 'تقارير التقييمات', description: 'عرض وتصدير تقارير التقييمات' },
            view_top_bottom_evaluations: { label: 'أعلى وأقل التقييمات', description: 'عرض أعلى وأقل التقييمات' },
            view_unevaluated_employees: { label: 'الموظفون غير المقيمين', description: 'عرض الموظفين الذين لم يتم تقييمهم' }
        }
    },
    contributions: {
        title: 'إدارة المساهمات',
        icon: 'fas fa-hand-holding-usd',
        permissions: {
            view_contributions: { label: 'عرض المساهمات', description: 'الوصول لصفحة المساهمات' },
            add_contribution: { label: 'إضافة مساهمة', description: 'إمكانية إضافة مساهمات جديدة' },
            edit_contribution: { label: 'تعديل المساهمة', description: 'إمكانية تعديل بيانات المساهمات' },
            delete_contribution: { label: 'حذف المساهمة', description: 'إمكانية حذف المساهمات' },
            view_contributions_list: { label: 'قائمة المساهمات', description: 'عرض قائمة جميع المساهمات' },
            view_contributions_reports: { label: 'تقارير المساهمات', description: 'عرض وتصدير تقارير المساهمات' }
        }
    },
    rewards_deductions: {
        title: 'المكافآت والخصومات',
        icon: 'fas fa-coins',
        permissions: {
            view_rewards_deductions: { label: 'عرض المكافآت والخصومات', description: 'الوصول للصفحة الرئيسية' },
            view_rewards_list: { label: 'قائمة المكافآت', description: 'عرض قائمة المكافآت' },
            add_reward: { label: 'إضافة مكافأة', description: 'إمكانية إضافة مكافآت جديدة' },
            edit_reward: { label: 'تعديل المكافأة', description: 'إمكانية تعديل بيانات المكافآت' },
            delete_reward: { label: 'حذف المكافأة', description: 'إمكانية حذف المكافآت' },
            export_rewards: { label: 'تصدير المكافآت', description: 'تصدير بيانات المكافآت' },
            view_deductions_list: { label: 'قائمة الخصومات', description: 'عرض قائمة الخصومات' },
            add_deduction: { label: 'إضافة خصم', description: 'إمكانية إضافة خصومات جديدة' },
            edit_deduction: { label: 'تعديل الخصم', description: 'إمكانية تعديل بيانات الخصومات' },
            delete_deduction: { label: 'حذف الخصم', description: 'إمكانية حذف الخصومات' },
            export_deductions: { label: 'تصدير الخصومات', description: 'تصدير بيانات الخصومات' },
            view_rewards_deductions_reports: { label: 'التقارير', description: 'عرض تقارير المكافآت والخصومات' }
        }
    },
    custody: {
        title: 'إدارة العهد',
        icon: 'fas fa-box',
        permissions: {
            view_custody: { label: 'عرض العهد', description: 'الوصول لصفحة العهد' },
            add_custody: { label: 'إضافة عهدة', description: 'إمكانية إضافة عهد جديدة' },
            edit_custody: { label: 'تعديل العهدة', description: 'إمكانية تعديل بيانات العهد' },
            delete_custody: { label: 'حذف العهدة', description: 'إمكانية حذف العهد' },
            deliver_custody: { label: 'تسليم عهدة', description: 'إمكانية تسليم العهد للموظفين' },
            edit_deliver_custody: { label: 'تعديل تسليم العهدة', description: 'تعديل بيانات تسليم العهد' },
            delete_deliver_custody: { label: 'حذف تسليم العهدة', description: 'حذف سجلات تسليم العهد' },
            view_undelivered_custody: { label: 'العهد غير المسلمة', description: 'عرض العهد التي لم يتم تسليمها' },
            view_employee_custody: { label: 'عهد الموظف', description: 'عرض العهد الخاصة بموظف معين' }
        }
    },
    resignations: {
        title: 'إدارة الاستقالات',
        icon: 'fas fa-sign-out-alt',
        permissions: {
            view_resignations: { label: 'عرض الاستقالات', description: 'الوصول لصفحة الاستقالات' },
            add_resignation: { label: 'إضافة استقالة', description: 'إمكانية إضافة استقالات جديدة' },
            edit_resignation: { label: 'تعديل الاستقالة', description: 'إمكانية تعديل بيانات الاستقالة' },
            delete_resignation: { label: 'حذف الاستقالة', description: 'إمكانية حذف الاستقالات' },
            view_resignation_reports: { label: 'تقارير الاستقالات', description: 'عرض وتصدير تقارير الاستقالات' }
        }
    },
    extra_hours: {
        title: 'الساعات الإضافية',
        icon: 'fas fa-clock',
        permissions: {
            view_extra_hours: { label: 'عرض الساعات الإضافية', description: 'الوصول لصفحة الساعات الإضافية' },
            add_extra_hour: { label: 'إضافة ساعات إضافية', description: 'إمكانية إضافة ساعات إضافية' },
            edit_extra_hour: { label: 'تعديل الساعات الإضافية', description: 'إمكانية تعديل الساعات الإضافية' },
            delete_extra_hour: { label: 'حذف الساعات الإضافية', description: 'إمكانية حذف الساعات الإضافية' },
            view_extra_hour_reports: { label: 'تقارير الساعات الإضافية', description: 'عرض وتصدير التقارير' }
        }
    },
    ideal_employees: {
        title: 'العامل المثالي',
        icon: 'fas fa-trophy',
        permissions: {
            view_ideal_employee: { label: 'عرض العامل المثالي', description: 'الوصول لصفحة العامل المثالي' },
            add_ideal_employee: { label: 'إضافة عامل مثالي', description: 'إمكانية إضافة عامل مثالي جديد' },
            edit_ideal_employee: { label: 'تعديل العامل المثالي', description: 'إمكانية تعديل بيانات العامل المثالي' },
            delete_ideal_employee: { label: 'حذف العامل المثالي', description: 'إمكانية حذف العامل المثالي' },
            view_ideal_employee_reports: { label: 'تقارير العامل المثالي', description: 'عرض وتصدير تقارير العامل المثالي' },
            export_ideal_employee_data: { label: 'تصدير بيانات العامل المثالي', description: 'تصدير بيانات العامل المثالي' }
        }
    },
    salary_advances: {
        title: 'إدارة السلف',
        icon: 'fas fa-money-bill-wave',
        permissions: {
            view_salary_advances: { label: 'عرض السلف', description: 'الوصول لصفحة السلف' },
            add_salary_advance: { label: 'إضافة سلفة', description: 'إمكانية إضافة سلف جديدة' },
            edit_salary_advance: { label: 'تعديل السلفة', description: 'إمكانية تعديل بيانات السلف' },
            delete_salary_advance: { label: 'حذف السلفة', description: 'إمكانية حذف السلف' },
            view_salary_advance_reports: { label: 'تقارير السلف', description: 'عرض وتصدير تقارير السلف' }
        }
    },
    system: {
        title: 'إدارة النظام',
        icon: 'fas fa-cog',
        permissions: {
            view_import: { label: 'استيراد البيانات', description: 'الوصول لصفحة استيراد البيانات' },
            manage_users: { label: 'إدارة المستخدمين', description: 'إدارة المستخدمين والصلاحيات' },
            view_activity_logs: { label: 'عرض سجل الأنشطة', description: 'الوصول لسجل أنشطة النظام' },
            reset_system: { label: 'إعادة تعيين النظام', description: 'إعادة تعيين النظام (عملية خطيرة)' }
        }
    }
};

// تحميل المستخدمين
async function loadUsers() {
    try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/users`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('فشل في تحميل المستخدمين');
        }

        const users = await response.json();
        const userSelect = document.getElementById('userSelect');
        
        userSelect.innerHTML = '<option value="">-- اختر مستخدم --</option>';
        
        users.forEach(user => {
            const option = document.createElement('option');
            option.value = user.id;
            option.textContent = `${user.username} (ID: ${user.id})`;
            userSelect.appendChild(option);
        });

    } catch (error) {
        console.error('خطأ في تحميل المستخدمين:', error);
        alert('فشل في تحميل قائمة المستخدمين');
    }
}

// تحميل صلاحيات مستخدم محدد
async function loadUserPermissions(userId) {
    try {
        showLoading();
        
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/users/${userId}`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('فشل في تحميل بيانات المستخدم');
        }

        const user = await response.json();
        currentUser = user;
        
        // تحليل الصلاحيات
        let permissions = {};
        if (user.permissions) {
            permissions = typeof user.permissions === 'string' 
                ? JSON.parse(user.permissions) 
                : user.permissions;
        }
        
        originalPermissions = { ...permissions };
        renderPermissionsCards(permissions);

    } catch (error) {
        console.error('خطأ في تحميل صلاحيات المستخدم:', error);
        alert('فشل في تحميل صلاحيات المستخدم');
        showNoUserSelected();
    }
}

// عرض شاشة التحميل
function showLoading() {
    const container = document.getElementById('permissionsContainer');
    container.innerHTML = `
        <div class="loading">
            <i class="fas fa-spinner"></i>
            <h3>جاري تحميل الصلاحيات...</h3>
        </div>
    `;
}

// عرض رسالة عدم اختيار مستخدم
function showNoUserSelected() {
    const container = document.getElementById('permissionsContainer');
    container.innerHTML = `
        <div class="no-user-selected">
            <i class="fas fa-user-plus"></i>
            <h3>اختر مستخدماً لعرض وتعديل صلاحياته</h3>
            <p>يمكنك اختيار مستخدم من القائمة أعلاه لبدء إدارة صلاحياته</p>
        </div>
    `;
}

// رسم بطاقات الصلاحيات
function renderPermissionsCards(userPermissions) {
    const container = document.getElementById('permissionsContainer');
    
    let cardsHTML = '<div class="permissions-grid">';
    
    Object.keys(PERMISSIONS_STRUCTURE).forEach(categoryKey => {
        const category = PERMISSIONS_STRUCTURE[categoryKey];
        
        cardsHTML += `
            <div class="permission-card" data-category="${categoryKey}">
                <div class="card-header">
                    <div class="card-title">
                        <i class="${category.icon} card-icon"></i>
                        <span>${category.title}</span>
                    </div>
                    <div class="card-toggle">
                        <button class="toggle-all-btn" onclick="toggleCategoryPermissions('${categoryKey}')">
                            تحديد الكل
                        </button>
                    </div>
                </div>
                <div class="card-body">
        `;
        
        Object.keys(category.permissions).forEach(permissionKey => {
            const permission = category.permissions[permissionKey];
            const isChecked = userPermissions[permissionKey] === true;
            
            cardsHTML += `
                <div class="permission-item">
                    <input type="checkbox" 
                           class="permission-checkbox" 
                           id="${permissionKey}" 
                           data-permission="${permissionKey}"
                           ${isChecked ? 'checked' : ''}>
                    <label for="${permissionKey}" class="permission-label">
                        ${permission.label}
                        <div class="permission-description">${permission.description}</div>
                    </label>
                    <div class="status-indicator ${isChecked ? 'status-enabled' : 'status-disabled'}"></div>
                </div>
            `;
        });
        
        cardsHTML += `
                </div>
            </div>
        `;
    });
    
    cardsHTML += '</div>';
    container.innerHTML = cardsHTML;
    
    // إضافة مستمعي الأحداث للـ checkboxes
    addPermissionEventListeners();
}

// إضافة مستمعي الأحداث للصلاحيات
function addPermissionEventListeners() {
    const checkboxes = document.querySelectorAll('.permission-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const statusIndicator = this.parentElement.querySelector('.status-indicator');
            
            if (this.checked) {
                statusIndicator.classList.remove('status-disabled');
                statusIndicator.classList.add('status-enabled');
            } else {
                statusIndicator.classList.remove('status-enabled');
                statusIndicator.classList.add('status-disabled');
            }
            
            updateToggleAllButton(this.closest('.permission-card'));
        });
    });
}

// تحديث زر "تحديد الكل" لكل فئة
function updateToggleAllButton(card) {
    const checkboxes = card.querySelectorAll('.permission-checkbox');
    const checkedBoxes = card.querySelectorAll('.permission-checkbox:checked');
    const toggleBtn = card.querySelector('.toggle-all-btn');
    
    if (checkedBoxes.length === checkboxes.length) {
        toggleBtn.textContent = 'إلغاء الكل';
    } else {
        toggleBtn.textContent = 'تحديد الكل';
    }
}

// تبديل صلاحيات فئة كاملة
function toggleCategoryPermissions(categoryKey) {
    const card = document.querySelector(`[data-category="${categoryKey}"]`);
    const checkboxes = card.querySelectorAll('.permission-checkbox');
    const checkedBoxes = card.querySelectorAll('.permission-checkbox:checked');
    const toggleBtn = card.querySelector('.toggle-all-btn');
    
    const shouldCheck = checkedBoxes.length !== checkboxes.length;
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = shouldCheck;
        const statusIndicator = checkbox.parentElement.querySelector('.status-indicator');
        
        if (shouldCheck) {
            statusIndicator.classList.remove('status-disabled');
            statusIndicator.classList.add('status-enabled');
        } else {
            statusIndicator.classList.remove('status-enabled');
            statusIndicator.classList.add('status-disabled');
        }
    });
    
    toggleBtn.textContent = shouldCheck ? 'إلغاء الكل' : 'تحديد الكل';
}

// جمع الصلاحيات الحالية
function getCurrentPermissions() {
    const permissions = {};
    const checkboxes = document.querySelectorAll('.permission-checkbox');
    
    checkboxes.forEach(checkbox => {
        permissions[checkbox.dataset.permission] = checkbox.checked;
    });
    
    return permissions;
}

// حفظ الصلاحيات
async function savePermissions() {
    if (!currentUser) {
        alert('لم يتم اختيار مستخدم');
        return;
    }
    
    try {
        const permissions = getCurrentPermissions();
        const token = localStorage.getItem('token');
        
        const response = await fetch(`${API_URL}/users/${currentUser.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                username: currentUser.username,
                permissions: permissions
            })
        });
        
        if (!response.ok) {
            throw new Error('فشل في حفظ الصلاحيات');
        }
        
        originalPermissions = { ...permissions };
        alert('تم حفظ الصلاحيات بنجاح');
        
    } catch (error) {
        console.error('خطأ في حفظ الصلاحيات:', error);
        alert('فشل في حفظ الصلاحيات');
    }
}

// إعادة تعيين الصلاحيات
function resetPermissions() {
    if (!currentUser) {
        alert('لم يتم اختيار مستخدم');
        return;
    }
    
    if (confirm('هل أنت متأكد من إعادة تعيين الصلاحيات للحالة الأصلية؟')) {
        renderPermissionsCards(originalPermissions);
    }
}

// العودة لصفحة إدارة المستخدمين
function backToUsers() {
    window.location.href = 'users.html';
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحميل المستخدمين
    loadUsers();
    
    // إضافة مستمعي الأحداث
    document.getElementById('userSelect').addEventListener('change', function() {
        const userId = this.value;
        if (userId) {
            loadUserPermissions(userId);
        } else {
            showNoUserSelected();
            currentUser = null;
        }
    });
    
    document.getElementById('savePermissions').addEventListener('click', savePermissions);
    document.getElementById('resetPermissions').addEventListener('click', resetPermissions);
    document.getElementById('backToUsers').addEventListener('click', backToUsers);
    
    // التحقق من الصلاحيات
    const permissions = localStorage.getItem('permissions');
    if (!permissions || !JSON.parse(permissions).manage_users) {
        alert('ليس لديك صلاحية للوصول لهذه الصفحة');
        window.location.href = 'index.html';
        return;
    }
});
