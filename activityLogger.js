/**
 * نظام تسجيل الأنشطة (Activity Logger)
 * يقوم بتسجيل جميع العمليات التي يقوم بها المستخدمون في النظام
 */

const mysql = require('mysql2');

// إعدادات قاعدة البيانات (نفس الإعدادات المستخدمة في server.js)
let dbConfig = {
  host: "localhost",
  user: "root",
  password: "Hbkhbkhbk@123",
  database: "hassan",
  port: 3306,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

// إنشاء connection pool
let pool = mysql.createPool(dbConfig);

/**
 * تسجيل نشاط جديد في قاعدة البيانات
 * @param {Object} logData - بيانات النشاط المراد تسجيله
 * @param {number|null} logData.user_id - معرف المستخدم (اختياري)
 * @param {string} logData.username - اسم المستخدم
 * @param {string} logData.action_type - نوع العملية (add, edit, delete, login, logout, print, deliver, return)
 * @param {string} logData.module - القسم (employees, vacations, custody, contributions, rewards, penalties, etc.)
 * @param {string|number|null} logData.record_id - معرف السجل المتأثر (اختياري)
 * @param {string} logData.message - وصف الإجراء
 * @returns {Promise<boolean>} - true إذا تم التسجيل بنجاح، false في حالة الخطأ
 */
async function logAction(logData) {
  try {
    // التحقق من البيانات المطلوبة
    if (!logData.username || !logData.action_type || !logData.module || !logData.message) {
      console.error('Activity Logger: البيانات المطلوبة مفقودة');
      return false;
    }

    // إعداد الاستعلام
    const query = `
      INSERT INTO activity_logs (user_id, username, action_type, module, record_id, message, created_at)
      VALUES (?, ?, ?, ?, ?, ?, NOW())
    `;

    const values = [
      logData.user_id || null,
      logData.username,
      logData.action_type,
      logData.module,
      logData.record_id || null,
      logData.message
    ];

    // تنفيذ الاستعلام
    await pool.promise().query(query, values);
    
    console.log(`Activity logged: ${logData.username} - ${logData.action_type} - ${logData.module}`);
    return true;

  } catch (error) {
    console.error('خطأ في تسجيل النشاط:', error);
    return false;
  }
}

/**
 * الحصول على سجل الأنشطة مع إمكانية التصفية
 * @param {Object} filters - فلاتر البحث
 * @param {string|null} filters.username - اسم المستخدم
 * @param {string|null} filters.action_type - نوع العملية
 * @param {string|null} filters.module - القسم
 * @param {string|null} filters.date_from - تاريخ البداية (YYYY-MM-DD)
 * @param {string|null} filters.date_to - تاريخ النهاية (YYYY-MM-DD)
 * @param {number} filters.limit - عدد السجلات المطلوبة (افتراضي: 100)
 * @param {number} filters.offset - نقطة البداية (افتراضي: 0)
 * @returns {Promise<Array>} - مصفوفة السجلات
 */
async function getActivityLogs(filters = {}) {
  try {
    let query = `
      SELECT 
        id,
        user_id,
        username,
        action_type,
        module,
        record_id,
        message,
        created_at,
        created_at,
        DATE_FORMAT(created_at, '%d/%m/%Y %H:%i:%s') as formatted_date
      FROM activity_logs
      WHERE 1=1
    `;
    
    const values = [];

    // إضافة الفلاتر
    if (filters.username) {
      query += ` AND username LIKE ?`;
      values.push(`%${filters.username}%`);
    }

    if (filters.action_type) {
      query += ` AND action_type = ?`;
      values.push(filters.action_type);
    }

    if (filters.module) {
      query += ` AND module = ?`;
      values.push(filters.module);
    }

    if (filters.date_from) {
      query += ` AND DATE(created_at) >= ?`;
      values.push(filters.date_from);
    }

    if (filters.date_to) {
      query += ` AND DATE(created_at) <= ?`;
      values.push(filters.date_to);
    }

    // ترتيب النتائج
    query += ` ORDER BY created_at DESC`;

    // إضافة الحد الأقصى للنتائج
    const limit = parseInt(filters.limit) || 100;
    const offset = parseInt(filters.offset) || 0;
    query += ` LIMIT ? OFFSET ?`;
    values.push(limit, offset);

    const [rows] = await pool.promise().query(query, values);

    // تحويل التاريخ إلى نظام 12 ساعة عربي
    return rows.map(log => ({
      ...log,
      formatted_date: formatDateFor12Hour(log.created_at)
    }));

  } catch (error) {
    console.error('خطأ في جلب سجل الأنشطة:', error);
    return [];
  }
}

/**
 * الحصول على إحصائيات سجل الأنشطة
 * @param {Object} filters - فلاتر البحث (نفس فلاتر getActivityLogs)
 * @returns {Promise<Object>} - إحصائيات السجل
 */
async function getActivityStats(filters = {}) {
  try {
    let query = `
      SELECT 
        COUNT(*) as total_count,
        COUNT(DISTINCT username) as unique_users,
        COUNT(DISTINCT module) as unique_modules,
        COUNT(DISTINCT action_type) as unique_actions
      FROM activity_logs
      WHERE 1=1
    `;
    
    const values = [];

    // إضافة نفس الفلاتر
    if (filters.username) {
      query += ` AND username LIKE ?`;
      values.push(`%${filters.username}%`);
    }

    if (filters.action_type) {
      query += ` AND action_type = ?`;
      values.push(filters.action_type);
    }

    if (filters.module) {
      query += ` AND module = ?`;
      values.push(filters.module);
    }

    if (filters.date_from) {
      query += ` AND DATE(created_at) >= ?`;
      values.push(filters.date_from);
    }

    if (filters.date_to) {
      query += ` AND DATE(created_at) <= ?`;
      values.push(filters.date_to);
    }

    const [rows] = await pool.promise().query(query, values);
    return rows[0] || { total_count: 0, unique_users: 0, unique_modules: 0, unique_actions: 0 };

  } catch (error) {
    console.error('خطأ في جلب إحصائيات الأنشطة:', error);
    return { total_count: 0, unique_users: 0, unique_modules: 0, unique_actions: 0 };
  }
}

/**
 * إنشاء رسالة تعديل مفصلة تظهر التغييرات قبل وبعد التعديل
 * @param {string} entityName - اسم الكيان (موظف، إجازة، إلخ)
 * @param {Object} oldData - البيانات القديمة
 * @param {Object} newData - البيانات الجديدة
 * @param {Object} fieldLabels - تسميات الحقول باللغة العربية
 * @returns {string} - رسالة التعديل المفصلة
 */
function createEditMessage(entityName, oldData, newData, fieldLabels = {}) {
  try {
    const changes = [];

    // معالجة مسبقة للبيانات القديمة لتحويل Date objects إلى strings منسقة - محدث لاستخدام DateFormatter
    const processedOldData = {};
    for (const [key, value] of Object.entries(oldData)) {
      if (value instanceof Date) {
        // استخدام DateFormatter الجديد لتنسيق التواريخ
        if (typeof DateFormatter !== 'undefined') {
          processedOldData[key] = DateFormatter.formatDateTime(value);
        } else {
          // Fallback للطريقة القديمة
          processedOldData[key] = value.toLocaleDateString('ar-EG', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
          });
        }
      } else {
        processedOldData[key] = value;
      }
    }

    // مقارنة الحقول وإنشاء قائمة التغييرات
    for (const [key, newValue] of Object.entries(newData)) {
      const oldValue = processedOldData[key];

      // تجاهل الحقول الفنية
      if (['id', 'created_at', 'updated_at'].includes(key)) {
        continue;
      }

      // الحصول على تسمية الحقل باللغة العربية
      const fieldLabel = fieldLabels[key] || key;

      // تنسيق القيم
      const formattedOldValue = formatValue(oldValue);
      const formattedNewValue = formatValue(newValue);

      // مقارنة القيم المنسقة بدلاً من القيم الخام
      if (formattedOldValue !== formattedNewValue) {
        changes.push(`${fieldLabel}: من "${formattedOldValue}" إلى "${formattedNewValue}"`);
      }
    }

    if (changes.length === 0) {
      return `تم تعديل ${entityName} (لا توجد تغييرات مرئية)`;
    }

    return `تم تعديل ${entityName} - التغييرات: ${changes.join(' | ')}`;

  } catch (error) {
    console.error('خطأ في إنشاء رسالة التعديل:', error);
    return `تم تعديل ${entityName}`;
  }
}

/**
 * تنسيق التاريخ لنظام 12 ساعة عربي لجدول سجل الأنشطة
 * @param {Date} date - التاريخ المراد تنسيقه
 * @returns {string} - التاريخ المنسق بنظام 12 ساعة
 */
function formatDateFor12Hour(date) {
  if (!date) return '';

  try {
    const dateObj = new Date(date);
    return dateObj.toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  } catch (error) {
    console.error('خطأ في تنسيق التاريخ:', error);
    return date.toString();
  }
}

/**
 * تنسيق القيم للعرض
 * @param {any} value - القيمة المراد تنسيقها
 * @returns {string} - القيمة المنسقة
 */
function formatValue(value) {
  if (value === null || value === undefined) {
    return 'غير محدد';
  }

  if (typeof value === 'boolean') {
    return value ? 'نعم' : 'لا';
  }

  if (typeof value === 'number') {
    return value.toString();
  }

  // تنسيق التواريخ - محدث لاستخدام DateFormatter
  if (value instanceof Date) {
    // استخدام DateFormatter الجديد
    if (typeof DateFormatter !== 'undefined') {
      return DateFormatter.formatDateTime(value);
    }

    // Fallback للطريقة القديمة
    return value.toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  }

  if (typeof value === 'string') {
    // تنسيق التواريخ النصية - استخدام DateFormatter أولاً
    if (typeof DateFormatter !== 'undefined') {
      // التحقق من أنماط التواريخ المختلفة
      if (value.match(/^\d{4}-\d{2}-\d{2}$/) ||
          value.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/) ||
          value.match(/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun)\s+\w+\s+\d{1,2}\s+\d{4}/) ||
          (value.includes('GMT') && value.match(/\d{4}/))) {

        if (DateFormatter.isValidDate(value)) {
          return DateFormatter.formatDateTime(value);
        }
      }
    }

    // Fallback للطريقة القديمة
    // تنسيق التواريخ النصية بصيغة YYYY-MM-DD
    if (value.match(/^\d{4}-\d{2}-\d{2}$/)) {
      try {
        return new Date(value + 'T12:00:00').toLocaleDateString('ar-EG', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        });
      } catch (e) {
        // في حالة فشل التحويل
      }
    }

    // تنسيق التواريخ النصية الطويلة
    if (value.match(/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun)\s+\w+\s+\d{1,2}\s+\d{4}/)) {
      try {
        return new Date(value).toLocaleDateString('ar-EG', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        });
      } catch (e) {
        // في حالة فشل التحويل
      }
    }

    // تنسيق التواريخ النصية بصيغة ISO
    if (value.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)) {
      try {
        return new Date(value).toLocaleDateString('ar-EG', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        });
      } catch (e) {
        // في حالة فشل التحويل
      }
    }

    // تنسيق أي نص يحتوي على GMT
    if (value.includes('GMT') && value.match(/\d{4}/)) {
      try {
        return new Date(value).toLocaleDateString('ar-EG', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        });
      } catch (e) {
        // في حالة فشل التحويل، نعيد النص كما هو
      }
    }

    return value.trim();
  }

  return String(value);
}

/**
 * حذف السجلات القديمة (تنظيف قاعدة البيانات)
 * @param {number} daysToKeep - عدد الأيام المراد الاحتفاظ بها (افتراضي: 90 يوم)
 * @returns {Promise<number>} - عدد السجلات المحذوفة
 */
async function cleanOldLogs(daysToKeep = 90) {
  try {
    const query = `
      DELETE FROM activity_logs
      WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
    `;

    const [result] = await pool.promise().query(query, [daysToKeep]);

    console.log(`تم حذف ${result.affectedRows} سجل قديم من سجل الأنشطة`);
    return result.affectedRows;

  } catch (error) {
    console.error('خطأ في حذف السجلات القديمة:', error);
    return 0;
  }
}

// تصدير الدوال
module.exports = {
  logAction,
  getActivityLogs,
  getActivityStats,
  cleanOldLogs,
  createEditMessage
};

// أمثلة على الاستخدام:

/*
// مثال 1: تسجيل عملية حذف موظف
await logAction({
  user_id: 1,
  username: 'admin',
  action_type: 'delete',
  module: 'employees',
  record_id: '12345',
  message: 'تم حذف الموظف: أحمد محمد علي (كود: 12345)'
});

// مثال 2: تسجيل عملية تسجيل دخول
await logAction({
  username: 'admin',
  action_type: 'login',
  module: 'system',
  message: 'تسجيل دخول المستخدم إلى النظام'
});

// مثال 3: تسجيل عملية تسليم عهدة
await logAction({
  user_id: 1,
  username: 'admin',
  action_type: 'deliver',
  module: 'custody',
  record_id: 'OP001',
  message: 'تم تسليم عهدة: لابتوب ديل (كود: LAP001) للموظف: أحمد محمد (كود: 12345)'
});

// مثال 4: تسجيل عملية طباعة تقرير
await logAction({
  user_id: 1,
  username: 'admin',
  action_type: 'print',
  module: 'vacations',
  message: 'تم طباعة تقرير الإجازات للفترة من 2024-01-01 إلى 2024-01-31'
});
*/
