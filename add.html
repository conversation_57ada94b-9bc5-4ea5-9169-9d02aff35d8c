<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <title>إضافة موظف جديد</title>

  <link rel="stylesheet" href="style.css">
  <link rel="stylesheet" href="add.css">
  <link rel="stylesheet" href="no-sidebar-layout.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">



  <script src="permissions.js" defer></script>
</head>
<body class="add-page">

  <!-- زر العودة للوحة التحكم -->
  <div class="back-to-dashboard">
    <a href="dashboard.html" class="dashboard-btn">
      <i class="fas fa-home"></i>
      <span>العودة للوحة التحكم</span>
    </a>
  </div>

  <!-- المحتوى الرئيسي -->
  <div class="main-content full-width" id="mainContent">
    <h1 class="form-title">إضافة موظف جديد</h1>
    <form id="addEmployeeForm" class="modern-form">
      <div class="form-grid">
        <div class="form-group">
          <label>كود الموظف</label>
          <input type="text" name="code" id="employeeCode" readonly required style="background-color: #f8f9fa; cursor: not-allowed;" />
        </div>
        <div class="form-group">
          <label>الاسم الكامل</label>
          <input type="text" name="full_name" required />
        </div>
        <hr class="form-separator">
        <div class="form-group">
          <label>الإدارة</label>
          <select name="department" id="departmentSelect" required>
            <option value="">اختر الإدارة</option>
          </select>
        </div>
        <div class="form-group">
          <label>الوظيفة</label>
          <input type="text" name="job_title" />
        </div>
        <div class="form-group">
          <label>تاريخ التعيين <i class="fa-regular fa-calendar-days"></i></label>
          <input type="date" name="hire_date" />
        </div>
        <div class="form-group">
          <label>العنوان</label>
          <input type="text" name="address" />
        </div>
        <div class="form-group">
          <label>المؤهل</label>
          <input type="text" name="qualification" />
        </div>
        <div class="form-group">
          <label>التليفون</label>
          <input type="text" name="phone" />
        </div>
        <div class="form-group">
          <label>تاريخ الميلاد <i class="fa-regular fa-calendar-days"></i></label>
          <input type="date" name="birth_date" />
        </div>
        <div class="form-group">
          <label>الحالة الاجتماعية</label>
          <input type="text" name="marital_status" />
        </div>
        <div class="form-group">
          <label>عدد الأبناء</label>
          <input type="text" name="children" />
        </div>
        <hr class="form-separator">
        <div class="form-group">
          <label>الرقم القومي</label>
          <input type="text" name="national_id" />
        </div>
        <div class="form-group">
          <label>التأمين التكافلي</label>
          <input type="text" name="social_insurance" />
        </div>
        <div class="form-group">
          <label>الرقم التأميني</label>
          <input type="text" name="insurance_number" />
        </div>
        <div class="form-group">
          <label>جهة التأمين</label>
          <input type="text" name="insurance_entity" />
        </div>
        <div class="form-group">
          <label>تاريخ التأمين عليه <i class="fa-regular fa-calendar-days"></i></label>
          <input type="date" name="insurance_start" />
        </div>
        <div class="form-group">
          <label>المهنة في التأمينات</label>
          <input type="text" name="insurance_job" />
        </div>
        <div class="form-group">
          <label>راتب التأمينات</label>
          <input type="text" name="insurance_salary" />
        </div>
        <div class="form-group">
          <label>ما يتحمله العامل</label>
          <input type="text" name="worker_cost" />
        </div>
        <div class="form-group">
          <label>ما تتحمله الشركة</label>
          <input type="text" name="company_cost" />
        </div>
        <div class="form-group">
          <label>الأجر الشامل</label>
          <input type="text" name="total_salary" />
        </div>
        <hr class="form-separator">
        <div class="form-group">
          <label>رقم البطاقة الصحية</label>
          <input type="text" name="health_card" />
        </div>
        <div class="form-group">
          <label>قياس المهارة</label>
          <input type="text" name="skill_level" />
        </div>
        <div class="form-group">
          <label>تاريخ بداية قياس المهارة <i class="fa-regular fa-calendar-days"></i></label>
          <input type="date" name="skill_start" />
        </div>
        <div class="form-group">
          <label>تاريخ انتهاء قياس المهارة <i class="fa-regular fa-calendar-days"></i></label>
          <input type="date" name="skill_end" />
        </div>
        <div class="form-group">
          <label>الوقت المتبقى على انتهاء قياس المهارة</label>
          <input type="text" name="skill_remaining" />
        </div>
        <div class="form-group">
          <label>مهنة قياس المهارة</label>
          <input type="text" name="skill_job" />
        </div>
        <hr class="form-separator">
        <div class="form-group">
          <label>رصيد الإجازات</label>
          <input type="text" name="leave_balance" />
        </div>
        <div class="form-group">
          <label>الإجازات المستخدمة</label>
          <input type="text" name="leave_used" />
        </div>
        <div class="form-group">
          <label>الإجازات المتبقية</label>
          <input type="text" name="leave_remaining" />
        </div>
        <div class="form-group">
          <label>ذوي الهمم</label>
          <input type="text" name="special_needs" />
        </div>
        <hr class="form-separator">
        <div class="form-group file-upload-section">
          <label>صورة الموظف</label>
          <div class="file-upload-container">
            <input type="file" id="employeePhoto" name="photo" accept="image/jpeg,image/jpg,image/png" class="file-input">
            <label for="employeePhoto" class="file-upload-label">
              <i class="fas fa-camera"></i>
              <span>اختر صورة الموظف</span>
            </label>
            <div class="file-preview" id="photoPreview" style="display: none;">
              <img id="photoPreviewImg" src="" alt="معاينة الصورة">
              <button type="button" class="remove-file-btn" onclick="removePhotoPreview()">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
          <small class="file-help-text">يُسمح بملفات JPG و PNG فقط، الحد الأقصى 5 ميجابايت</small>
        </div>
        <div class="form-group file-upload-section">
          <label>مستندات الموظف</label>
          <div class="file-upload-container">
            <input type="file" id="employeeDocuments" name="documents" multiple
                   accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.txt" class="file-input">
            <label for="employeeDocuments" class="file-upload-label">
              <i class="fas fa-file-upload"></i>
              <span>اختر المستندات</span>
            </label>
            <div class="documents-preview" id="documentsPreview"></div>
          </div>
          <small class="file-help-text">يُسمح بملفات PDF, DOC, DOCX, XLS, XLSX, JPG, PNG, TXT، الحد الأقصى 10 ميجابايت لكل ملف</small>
        </div>
      </div>
      <button type="submit" class="submit-btn">إضافة</button>
    </form>
  </div>

  <!-- نافذة إضافة إدارة جديدة -->
  <div id="addDepartmentModal" class="modal" style="display: none;">
    <div class="modal-content">
      <span class="close" id="closeDepartmentModal">&times;</span>
      <h3>إضافة إدارة جديدة</h3>
      <form id="addDepartmentForm">
        <div class="form-group">
          <label for="newDepartmentName">اسم الإدارة الجديدة:</label>
          <input type="text" id="newDepartmentName" required placeholder="أدخل اسم الإدارة">
        </div>
        <div class="modal-actions">
          <button type="submit" class="btn-primary">إضافة</button>
          <button type="button" class="btn-secondary" id="cancelDepartmentModal">إلغاء</button>
        </div>
      </form>
    </div>
  </div>

  <!-- تحميل Day.js للتواريخ -->
  <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/dayjs.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/plugin/utc.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/plugin/timezone.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/plugin/customParseFormat.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/plugin/localizedFormat.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/locale/ar.js"></script>

  <!-- تحميل DateFormatter الموحد -->
  <script src="utils/dateFormatter-browser.js"></script>

  <script>
    // تهيئة Day.js
    if (typeof dayjs !== 'undefined') {
      dayjs.extend(dayjs_plugin_utc);
      dayjs.extend(dayjs_plugin_timezone);
      dayjs.extend(dayjs_plugin_customParseFormat);
      dayjs.extend(dayjs_plugin_localizedFormat);
      dayjs.locale('ar');
      console.log('DateFormatter تم تحميله بنجاح في add.html');
    }
  </script>
  <script>
    // تحميل الأقسام والكود التلقائي عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', async function() {

      await loadDepartments();
      await loadNextEmployeeCode();
      setupDepartmentModal();
    });

    // وظيفة تحميل الكود التلقائي للموظف
    async function loadNextEmployeeCode() {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch('http://localhost:5500/api/employees/next-code', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        if (response.ok) {
          const data = await response.json();
          console.log('تم تحميل الكود التالي:', data.nextCode);
          document.getElementById('employeeCode').value = data.nextCode;
        } else {
          console.error('فشل في تحميل الكود التلقائي - رمز الاستجابة:', response.status);
          // محاولة الحصول على آخر كود من جدول الموظفين
          await loadNextCodeFromEmployees();
        }
      } catch (error) {
        console.error('خطأ في تحميل الكود التلقائي:', error);
        // محاولة الحصول على آخر كود من جدول الموظفين
        await loadNextCodeFromEmployees();
      }
    }
    
    // وظيفة بديلة للحصول على آخر كود من جدول الموظفين
    async function loadNextCodeFromEmployees() {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch('http://localhost:5500/api/employees', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        if (response.ok) {
          const employees = await response.json();
          let maxCode = 0;
          employees.forEach(emp => {
            const code = parseInt(emp.code);
            if (!isNaN(code) && code > maxCode) {
              maxCode = code;
            }
          });
          const nextCode = maxCode + 1;
          console.log('تم حساب الكود التالي من جدول الموظفين:', nextCode);
          document.getElementById('employeeCode').value = nextCode.toString();
        } else {
          console.error('فشل في تحميل بيانات الموظفين');
          document.getElementById('employeeCode').value = '1';
        }
      } catch (error) {
        console.error('خطأ في تحميل بيانات الموظفين:', error);
        document.getElementById('employeeCode').value = '1';
      }
    }

    // تحميل قائمة الأقسام
    async function loadDepartments() {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch('http://localhost:5500/api/departments', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        const departments = await response.json();
        
        const departmentSelect = document.getElementById('departmentSelect');
        departmentSelect.innerHTML = '<option value="">اختر الإدارة</option>';
        
        departments.forEach(dept => {
          const option = document.createElement('option');
          option.value = dept;
          option.textContent = dept;
          departmentSelect.appendChild(option);
        });
        
        // إضافة خيار "إدارة جديدة"
        const newDeptOption = document.createElement('option');
        newDeptOption.value = 'new_department';
        newDeptOption.textContent = '+ إدارة جديدة';
        newDeptOption.style.fontWeight = 'bold';
        newDeptOption.style.color = '#007bff';
        departmentSelect.appendChild(newDeptOption);
        
      } catch (error) {
        console.error('خطأ في تحميل الأقسام:', error);
      }
    }

    // إعداد النافذة المنبثقة للإدارة
    function setupDepartmentModal() {
      const departmentSelect = document.getElementById('departmentSelect');
      const modal = document.getElementById('addDepartmentModal');
      const closeBtn = document.getElementById('closeDepartmentModal');
      const cancelBtn = document.getElementById('cancelDepartmentModal');
      const form = document.getElementById('addDepartmentForm');
      
      // عند اختيار "إدارة جديدة"
      departmentSelect.addEventListener('change', function() {
        if (this.value === 'new_department') {
          modal.style.display = 'block';
          document.getElementById('newDepartmentName').focus();
        }
      });
      
      // إغلاق النافذة
      function closeModal() {
        modal.style.display = 'none';
        departmentSelect.value = '';
        document.getElementById('newDepartmentName').value = '';
      }
      
      closeBtn.addEventListener('click', closeModal);
      cancelBtn.addEventListener('click', closeModal);
      
      // إغلاق عند النقر خارج النافذة
      window.addEventListener('click', function(event) {
        if (event.target === modal) {
          closeModal();
        }
      });
      
      // إضافة إدارة جديدة
      form.addEventListener('submit', async function(e) {
        e.preventDefault();
        const newDepartmentName = document.getElementById('newDepartmentName').value.trim();
        
        if (!newDepartmentName) {
          alert('يرجى إدخال اسم الإدارة');
          return;
        }
        
        // إضافة الإدارة الجديدة إلى القائمة
        const option = document.createElement('option');
        option.value = newDepartmentName;
        option.textContent = newDepartmentName;
        
        // إدراج الخيار الجديد قبل "إدارة جديدة"
        const newDeptOption = departmentSelect.querySelector('option[value="new_department"]');
        departmentSelect.insertBefore(option, newDeptOption);
        
        // اختيار الإدارة الجديدة
        departmentSelect.value = newDepartmentName;
        
        closeModal();
        alert('تمت إضافة الإدارة بنجاح');
      });
    }

    // إرسال نموذج إضافة الموظف
    document.getElementById("addEmployeeForm").addEventListener("submit", async function(e) {
      e.preventDefault();

      // التحقق من اختيار إدارة صحيحة
      const departmentValue = document.getElementById('departmentSelect').value;
      if (departmentValue === 'new_department' || !departmentValue) {
        alert('يرجى اختيار إدارة صحيحة');
        return;
      }

      try {
        // إنشاء البيانات الأساسية (بدون الملفات)
        const formData = new FormData(e.target);
        const data = {};
        formData.forEach((value, key) => {
          if (key !== 'photo' && key !== 'documents') {
            data[key] = value;
          }
        });

        // إرسال بيانات الموظف أولاً
        const token = localStorage.getItem('token');
        const employeeResponse = await fetch("http://localhost:5500/api/employees", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(data)
        });

        if (!employeeResponse.ok) {
          const errorData = await employeeResponse.json();
          throw new Error(errorData.error || "فشل في إضافة الموظف");
        }

        const employeeResult = await employeeResponse.json();
        const employeeCode = employeeResult.code || data.code;

        // رفع الصورة إذا تم اختيارها
        const photoFile = document.getElementById('employeePhoto').files[0];
        if (photoFile) {
          const photoFormData = new FormData();
          photoFormData.append('photo', photoFile);

          try {
            const token = localStorage.getItem('token');
            const photoResponse = await fetch(`http://localhost:5500/api/employees/${employeeCode}/photo`, {
              method: "POST",
              headers: {
                'Authorization': `Bearer ${token}`
              },
              body: photoFormData
            });

            if (!photoResponse.ok) {
              console.warn('فشل في رفع الصورة');
            }
          } catch (photoError) {
            console.warn('خطأ في رفع الصورة:', photoError);
          }
        }

        // رفع المستندات إذا تم اختيارها
        const documentFiles = document.getElementById('employeeDocuments').files;
        if (documentFiles.length > 0) {
          const documentsFormData = new FormData();

          for (let i = 0; i < documentFiles.length; i++) {
            documentsFormData.append('documents', documentFiles[i]);
            documentsFormData.append('displayNames', documentFiles[i].name);
          }

          try {
            const token = localStorage.getItem('token');
            const documentsResponse = await fetch(`http://localhost:5500/api/employees/${employeeCode}/documents`, {
              method: "POST",
              headers: {
                'Authorization': `Bearer ${token}`
              },
              body: documentsFormData
            });

            if (!documentsResponse.ok) {
              console.warn('فشل في رفع بعض المستندات');
            }
          } catch (documentsError) {
            console.warn('خطأ في رفع المستندات:', documentsError);
          }
        }

        alert("تمت إضافة الموظف بنجاح");

        // تحديث الكود للموظف التالي
        loadNextEmployeeCode();

        // إعادة تعيين النموذج
        document.getElementById("addEmployeeForm").reset();
        removePhotoPreview();
        document.getElementById('documentsPreview').innerHTML = '';

        // إعادة تحميل الأقسام
        loadDepartments();

      } catch (err) {
        console.error("خطأ في إضافة الموظف:", err);
        alert("فشل في إضافة الموظف: " + err.message);
      }
    });
  </script>

  <!-- JavaScript لمعالجة رفع الملفات -->
  <script>
    // معالجة معاينة الصورة
    document.getElementById('employeePhoto').addEventListener('change', function(e) {
      const file = e.target.files[0];
      const preview = document.getElementById('photoPreview');
      const previewImg = document.getElementById('photoPreviewImg');

      if (file) {
        // التحقق من نوع الملف
        if (!file.type.match('image.*')) {
          alert('يرجى اختيار ملف صورة صحيح');
          e.target.value = '';
          return;
        }

        // التحقق من حجم الملف (5MB)
        if (file.size > 5 * 1024 * 1024) {
          alert('حجم الصورة يجب أن يكون أقل من 5 ميجابايت');
          e.target.value = '';
          return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
          previewImg.src = e.target.result;
          preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
      } else {
        preview.style.display = 'none';
      }
    });

    // إزالة معاينة الصورة
    function removePhotoPreview() {
      document.getElementById('employeePhoto').value = '';
      document.getElementById('photoPreview').style.display = 'none';
    }

    // معالجة معاينة المستندات
    document.getElementById('employeeDocuments').addEventListener('change', function(e) {
      const files = Array.from(e.target.files);
      const preview = document.getElementById('documentsPreview');

      preview.innerHTML = '';

      files.forEach((file, index) => {
        // التحقق من حجم الملف (10MB)
        if (file.size > 10 * 1024 * 1024) {
          alert(`حجم الملف "${file.name}" يجب أن يكون أقل من 10 ميجابايت`);
          return;
        }

        const fileItem = document.createElement('div');
        fileItem.className = 'document-preview-item';
        fileItem.innerHTML = `
          <div class="document-info">
            <i class="fas fa-file"></i>
            <span class="document-name">${file.name}</span>
            <span class="document-size">(${formatFileSize(file.size)})</span>
          </div>
          <button type="button" class="remove-document-btn" onclick="removeDocument(${index})">
            <i class="fas fa-times"></i>
          </button>
        `;
        preview.appendChild(fileItem);
      });
    });

    // إزالة مستند من المعاينة
    function removeDocument(index) {
      const input = document.getElementById('employeeDocuments');
      const dt = new DataTransfer();
      const files = Array.from(input.files);

      files.forEach((file, i) => {
        if (i !== index) {
          dt.items.add(file);
        }
      });

      input.files = dt.files;
      input.dispatchEvent(new Event('change'));
    }

    // تنسيق حجم الملف
    function formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
  </script>


</body>
</html>
