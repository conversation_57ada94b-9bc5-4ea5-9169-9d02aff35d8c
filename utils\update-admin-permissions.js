/**
 * سكريپت لتحديث صلاحيات المستخدم admin ليشمل جميع الصلاحيات الجديدة
 */

const { pool } = require('../config/database');

async function updateAdminPermissions() {
    try {
        console.log('🔧 بدء تحديث صلاحيات المستخدم admin...\n');

        // جميع الصلاحيات الكاملة
        const allPermissions = {
            // الصلاحيات العامة
            can_view: true,
            can_add: true,
            can_edit: true,
            can_delete: true,

            // إدارة الموظفين
            view_employees: true,
            add_employees: true,
            edit_employees: true,
            delete_employees: true,
            export_employees: true,
            calculate_leave_balance: true,
            calculate_used_leave: true,
            delete_all_employees: true,

            // إدارة الإجازات
            view_vacations: true,
            add_vacation: true,
            view_vacations_list: true,
            view_vacation_reports: true,

            // إدارة التدريب
            view_training: true,
            add_training: true,
            edit_training: true,
            delete_training: true,
            view_training_reports: true,

            // إدارة التقييمات
            view_evaluation: true,
            add_evaluation: true,
            edit_evaluation: true,
            delete_evaluation: true,
            view_evaluation_reports: true,
            view_top_bottom_evaluations: true,
            view_unevaluated_employees: true,

            // إدارة المساهمات
            view_contributions: true,
            add_contribution: true,
            view_contributions_list: true,
            view_contributions_reports: true,

            // المكافآت والخصومات
            view_rewards_deductions: true,
            view_rewards_list: true,
            add_reward: true,
            export_rewards: true,
            view_deductions_list: true,
            add_deduction: true,
            export_deductions: true,
            view_rewards_deductions_reports: true,

            // إدارة العهد
            view_custody: true,
            add_custody: true,
            deliver_custody: true,
            edit_deliver_custody: true,
            delete_deliver_custody: true,
            edit_return_custody: true,
            delete_return_custody: true,
            view_undelivered_custody: true,
            view_employee_custody: true,

            // إدارة الاستقالات
            view_resignations: true,
            add_resignation: true,
            edit_resignation: true,
            delete_resignation: true,
            view_resignation_reports: true,

            // الساعات الإضافية
            view_extra_hours: true,
            add_extra_hour: true,
            edit_extra_hour: true,
            delete_extra_hour: true,
            view_extra_hour_reports: true,

            // العامل المثالي
            view_ideal_employee: true,
            add_ideal_employee: true,
            edit_ideal_employee: true,
            delete_ideal_employee: true,
            view_ideal_employee_reports: true,
            export_ideal_employee_data: true,

            // إدارة السلف
            view_salary_advances: true,
            add_salary_advance: true,
            edit_salary_advance: true,
            delete_salary_advance: true,
            view_salary_advance_reports: true,

            // إدارة النظام
            view_import: true,
            manage_users: true,
            view_activity_logs: true,
            reset_system: true
        };

        // تحديث صلاحيات المستخدم admin
        const [result] = await pool.promise().query(
            "UPDATE users SET permissions = ? WHERE username = 'admin'",
            [JSON.stringify(allPermissions)]
        );

        if (result.affectedRows > 0) {
            console.log('✅ تم تحديث صلاحيات المستخدم admin بنجاح!');
            console.log(`📊 تم تحديث ${Object.keys(allPermissions).length} صلاحية`);
            
            // عرض الصلاحيات المحدثة
            console.log('\n🔍 الصلاحيات المحدثة:');
            Object.keys(allPermissions).forEach((permission, index) => {
                if (index % 4 === 0) console.log(''); // سطر جديد كل 4 صلاحيات
                process.stdout.write(`✓ ${permission.padEnd(25)}`);
            });
            console.log('\n');
            
        } else {
            console.log('❌ لم يتم العثور على المستخدم admin');
        }

        // التحقق من التحديث
        const [adminUser] = await pool.promise().query(
            "SELECT permissions FROM users WHERE username = 'admin'"
        );

        if (adminUser.length > 0) {
            let savedPermissions = {};
            try {
                savedPermissions = typeof adminUser[0].permissions === 'string'
                    ? JSON.parse(adminUser[0].permissions)
                    : adminUser[0].permissions;
            } catch (parseError) {
                console.log('⚠️ خطأ في تحليل الصلاحيات المحفوظة:', parseError.message);
                savedPermissions = {};
            }

            const savedCount = Object.keys(savedPermissions).length;
            const expectedCount = Object.keys(allPermissions).length;

            console.log(`📈 إحصائيات التحديث:`);
            console.log(`   - الصلاحيات المحفوظة: ${savedCount}`);
            console.log(`   - الصلاحيات المتوقعة: ${expectedCount}`);
            console.log(`   - حالة التطابق: ${savedCount === expectedCount ? '✅ مطابق' : '❌ غير مطابق'}`);
        }

        console.log('\n🎉 انتهى تحديث صلاحيات المستخدم admin!');

    } catch (error) {
        console.error('❌ خطأ في تحديث صلاحيات admin:', error);
    }
}

// تشغيل السكريپت
if (require.main === module) {
    updateAdminPermissions().then(() => {
        process.exit(0);
    }).catch(error => {
        console.error('خطأ:', error);
        process.exit(1);
    });
}

module.exports = { updateAdminPermissions };
