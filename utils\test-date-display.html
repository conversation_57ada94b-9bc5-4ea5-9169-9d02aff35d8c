<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار عرض التواريخ - DateFormatter</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 3px;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        th {
            background-color: #f8f9fa;
        }
        .status {
            font-weight: bold;
        }
        .status.ok { color: green; }
        .status.error { color: red; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار عرض التواريخ - DateFormatter</h1>
        <p>هذا الاختبار يتحقق من عمل تنسيق التواريخ في جميع أنحاء النظام</p>

        <div class="test-section">
            <h3>📚 تحميل المكتبات</h3>
            <div id="library-status"></div>
        </div>

        <div class="test-section">
            <h3>🔧 اختبار دوال DateFormatter</h3>
            <div id="formatter-tests"></div>
        </div>

        <div class="test-section">
            <h3>📅 اختبار تنسيقات مختلفة</h3>
            <table>
                <thead>
                    <tr>
                        <th>التاريخ المدخل</th>
                        <th>formatDate</th>
                        <th>formatDateLong</th>
                        <th>formatDateForInput</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody id="format-tests"></tbody>
            </table>
        </div>

        <div class="test-section">
            <h3>🧮 اختبار الحسابات</h3>
            <div id="calculation-tests"></div>
        </div>

        <div class="test-section">
            <h3>🔄 اختبار التوافق مع الكود القديم</h3>
            <div id="compatibility-tests"></div>
        </div>

        <div class="test-section">
            <h3>📊 ملخص النتائج</h3>
            <div id="summary"></div>
        </div>
    </div>

    <!-- تحميل Day.js -->
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/dayjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/plugin/utc.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/plugin/timezone.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/plugin/customParseFormat.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/plugin/localizedFormat.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/locale/ar.js"></script>
    
    <!-- تحميل DateFormatter -->
    <script src="dateFormatter-browser.js"></script>
    
    <!-- تحميل المكتبات القديمة للاختبار -->
    <script src="../dateUtils.js"></script>
    <script src="../shared-utils.js"></script>

    <script>
        // تهيئة Day.js
        if (typeof dayjs !== 'undefined') {
            dayjs.extend(dayjs_plugin_utc);
            dayjs.extend(dayjs_plugin_timezone);
            dayjs.extend(dayjs_plugin_customParseFormat);
            dayjs.extend(dayjs_plugin_localizedFormat);
            dayjs.locale('ar');
        }

        let testResults = {
            passed: 0,
            failed: 0,
            total: 0
        };

        function runTest(name, testFunction) {
            testResults.total++;
            try {
                const result = testFunction();
                if (result) {
                    testResults.passed++;
                    return { status: 'success', message: `✅ ${name}: نجح` };
                } else {
                    testResults.failed++;
                    return { status: 'error', message: `❌ ${name}: فشل` };
                }
            } catch (error) {
                testResults.failed++;
                return { status: 'error', message: `❌ ${name}: خطأ - ${error.message}` };
            }
        }

        function displayResult(containerId, result) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${result.status}`;
            div.textContent = result.message;
            container.appendChild(div);
        }

        // اختبار تحميل المكتبات
        function testLibraryLoading() {
            const libraryStatus = document.getElementById('library-status');
            
            const tests = [
                { name: 'Day.js', condition: typeof dayjs !== 'undefined' },
                { name: 'DateFormatter', condition: typeof DateFormatter !== 'undefined' },
                { name: 'DateUtils (للتوافق)', condition: typeof DateUtils !== 'undefined' }
            ];

            tests.forEach(test => {
                const result = {
                    status: test.condition ? 'success' : 'error',
                    message: `${test.condition ? '✅' : '❌'} ${test.name}: ${test.condition ? 'محمل' : 'غير محمل'}`
                };
                displayResult('library-status', result);
            });
        }

        // اختبار دوال DateFormatter
        function testDateFormatterFunctions() {
            const tests = [
                {
                    name: 'formatDate',
                    test: () => DateFormatter.formatDate('2025-07-12') === '12/07/2025'
                },
                {
                    name: 'formatDateLong',
                    test: () => DateFormatter.formatDateLong('2025-07-12').includes('يوليو')
                },
                {
                    name: 'formatDateForInput',
                    test: () => DateFormatter.formatDateForInput('12/07/2025') === '2025-07-12'
                },
                {
                    name: 'calculateAge',
                    test: () => {
                        const age = DateFormatter.calculateAge('1990-01-01');
                        return age >= 30 && age <= 40;
                    }
                },
                {
                    name: 'isValidDate',
                    test: () => DateFormatter.isValidDate('2025-07-12') && !DateFormatter.isValidDate('invalid')
                }
            ];

            tests.forEach(test => {
                const result = runTest(test.name, test.test);
                displayResult('formatter-tests', result);
            });
        }

        // اختبار تنسيقات مختلفة
        function testDifferentFormats() {
            const testDates = [
                '2025-07-12',
                '12/07/2025',
                '2025-07-12T10:30:00Z',
                new Date('2025-07-12'),
                null,
                'invalid-date'
            ];

            const tbody = document.getElementById('format-tests');
            
            testDates.forEach(date => {
                const row = document.createElement('tr');
                
                const inputCell = document.createElement('td');
                inputCell.textContent = JSON.stringify(date);
                row.appendChild(inputCell);

                const formatCell = document.createElement('td');
                const longCell = document.createElement('td');
                const inputFormatCell = document.createElement('td');
                const statusCell = document.createElement('td');

                try {
                    const formatted = DateFormatter.formatDate(date);
                    const longFormatted = DateFormatter.formatDateLong(date);
                    const inputFormatted = DateFormatter.formatDateForInput(date);

                    formatCell.textContent = formatted;
                    longCell.textContent = longFormatted;
                    inputFormatCell.textContent = inputFormatted;

                    statusCell.innerHTML = '<span class="status ok">✅ نجح</span>';
                } catch (error) {
                    formatCell.textContent = 'خطأ';
                    longCell.textContent = 'خطأ';
                    inputFormatCell.textContent = 'خطأ';
                    statusCell.innerHTML = '<span class="status error">❌ فشل</span>';
                }

                row.appendChild(formatCell);
                row.appendChild(longCell);
                row.appendChild(inputFormatCell);
                row.appendChild(statusCell);
                tbody.appendChild(row);
            });
        }

        // اختبار الحسابات
        function testCalculations() {
            const tests = [
                {
                    name: 'حساب العمر (1990)',
                    test: () => {
                        const age = DateFormatter.calculateAge('1990-01-01');
                        return age >= 30 && age <= 40;
                    }
                },
                {
                    name: 'حساب مدة الخدمة (2020)',
                    test: () => {
                        const service = DateFormatter.calculateServiceDuration('2020-01-01');
                        return service && service.years >= 4;
                    }
                }
            ];

            tests.forEach(test => {
                const result = runTest(test.name, test.test);
                displayResult('calculation-tests', result);
            });
        }

        // اختبار التوافق مع الكود القديم
        function testCompatibility() {
            const tests = [
                {
                    name: 'DateUtils.formatDateArabic',
                    test: () => {
                        if (typeof DateUtils !== 'undefined') {
                            return DateUtils.formatDateArabic('2025-07-12') === '12/07/2025';
                        }
                        return true; // تجاهل إذا لم يكن متاحاً
                    }
                }
            ];

            tests.forEach(test => {
                const result = runTest(test.name, test.test);
                displayResult('compatibility-tests', result);
            });
        }

        // عرض ملخص النتائج
        function displaySummary() {
            const summary = document.getElementById('summary');
            const successRate = ((testResults.passed / testResults.total) * 100).toFixed(1);
            
            summary.innerHTML = `
                <div class="test-result ${testResults.failed === 0 ? 'success' : 'info'}">
                    📊 إجمالي الاختبارات: ${testResults.total}<br>
                    ✅ نجح: ${testResults.passed}<br>
                    ❌ فشل: ${testResults.failed}<br>
                    📈 معدل النجاح: ${successRate}%
                </div>
            `;

            if (testResults.failed === 0) {
                summary.innerHTML += '<div class="test-result success">🎉 جميع الاختبارات نجحت! DateFormatter يعمل بشكل مثالي.</div>';
            } else {
                summary.innerHTML += '<div class="test-result error">⚠️ بعض الاختبارات فشلت. يرجى مراجعة النتائج أعلاه.</div>';
            }
        }

        // تشغيل جميع الاختبارات
        function runAllTests() {
            console.log('🧪 بدء اختبار DateFormatter...');
            
            testLibraryLoading();
            testDateFormatterFunctions();
            testDifferentFormats();
            testCalculations();
            testCompatibility();
            displaySummary();
            
            console.log('✅ انتهى الاختبار');
        }

        // تشغيل الاختبارات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', runAllTests);
    </script>
</body>
</html>
