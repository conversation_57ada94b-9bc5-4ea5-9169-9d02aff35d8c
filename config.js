// ملف الإعدادات المشتركة لمشروع نظام إدارة الموظفين
// يحتوي على جميع المتغيرات والثوابت المشتركة بين الملفات

// إعدادات الخادم والAPI
const CONFIG = {
  // رابط API الأساسي
  API_URL: localStorage.getItem('serverUrl') || 'http://localhost:5500/api',
  
  // إعدادات الخادم
  SERVER: {
    DEFAULT_PORT: 5500,
    TIMEOUT: 30000, // 30 ثانية
    RETRY_ATTEMPTS: 3
  },
  
  // إعدادات التوكن والمصادقة
  AUTH: {
    TOKEN_KEY: 'token',
    TOKEN_EXPIRY_KEY: 'tokenExpiry',
    USER_KEY: 'currentUser',
    REMEMBER_SERVER_KEY: 'serverUrl'
  },
  
  // إعدادات التطبيق
  APP: {
    NAME: 'نظام إدارة الموظفين',
    VERSION: '2.0.0',
    LANGUAGE: 'ar',
    DIRECTION: 'rtl'
  },
  
  // إعدادات التواريخ
  DATES: {
    FORMAT: 'YYYY-MM-DD',
    DISPLAY_FORMAT: 'DD/MM/YYYY',
    LOCALE: 'ar-SA'
  },
  
  // إعدادات الجداول والعرض
  DISPLAY: {
    ITEMS_PER_PAGE: 50,
    MAX_SEARCH_RESULTS: 100,
    ANIMATION_DURATION: 300
  },
  
  // رسائل النظام
  MESSAGES: {
    SUCCESS: {
      SAVE: 'تم الحفظ بنجاح',
      DELETE: 'تم الحذف بنجاح',
      UPDATE: 'تم التحديث بنجاح',
      LOAD: 'تم التحميل بنجاح'
    },
    ERROR: {
      NETWORK: 'خطأ في الاتصال بالخادم',
      AUTH: 'خطأ في المصادقة',
      PERMISSION: 'ليس لديك صلاحية للقيام بهذا الإجراء',
      VALIDATION: 'يرجى التحقق من البيانات المدخلة',
      GENERAL: 'حدث خطأ غير متوقع'
    },
    CONFIRM: {
      DELETE: 'هل أنت متأكد من الحذف؟',
      SAVE: 'هل تريد حفظ التغييرات؟',
      CANCEL: 'هل تريد إلغاء العملية؟'
    }
  },
  
  // أنواع البيانات والحقول
  EMPLOYEE_FIELDS: {
    REQUIRED: ['code', 'full_name', 'department'],
    OPTIONAL: ['job_title', 'phone', 'email', 'hire_date'],
    NUMERIC: ['code', 'salary', 'leave_balance']
  },
  
  // إعدادات الصلاحيات
  PERMISSIONS: {
    ADMIN: 'admin',
    USER: 'user',
    VIEWER: 'viewer'
  },
  
  // أنواع الأنشطة للسجل
  ACTIVITY_TYPES: {
    CREATE: 'create',
    UPDATE: 'update',
    DELETE: 'delete',
    VIEW: 'view',
    LOGIN: 'login',
    LOGOUT: 'logout'
  }
};

// دوال مساعدة للوصول للإعدادات
const ConfigUtils = {
  // الحصول على رابط API
  getApiUrl() {
    return CONFIG.API_URL;
  },
  
  // تحديث رابط API
  setApiUrl(url) {
    CONFIG.API_URL = url;
    localStorage.setItem('serverUrl', url);
  },
  
  // الحصول على التوكن
  getToken() {
    return localStorage.getItem(CONFIG.AUTH.TOKEN_KEY);
  },
  
  // حفظ التوكن
  setToken(token) {
    localStorage.setItem(CONFIG.AUTH.TOKEN_KEY, token);
    localStorage.setItem(CONFIG.AUTH.TOKEN_EXPIRY_KEY, Date.now() + (24 * 60 * 60 * 1000)); // 24 ساعة
  },
  
  // التحقق من صلاحية التوكن
  isTokenValid() {
    const token = this.getToken();
    const expiry = localStorage.getItem(CONFIG.AUTH.TOKEN_EXPIRY_KEY);
    return token && expiry && Date.now() < parseInt(expiry);
  },
  
  // مسح بيانات المصادقة
  clearAuth() {
    localStorage.removeItem(CONFIG.AUTH.TOKEN_KEY);
    localStorage.removeItem(CONFIG.AUTH.TOKEN_EXPIRY_KEY);
    localStorage.removeItem(CONFIG.AUTH.USER_KEY);
  },
  
  // الحصول على headers للطلبات
  getAuthHeaders() {
    const token = this.getToken();
    return token ? { 'Authorization': `Bearer ${token}` } : {};
  },
  
  // تنسيق التاريخ - محدث لاستخدام DateFormatter
  formatDate(date, format = CONFIG.DATES.DISPLAY_FORMAT) {
    // استخدام DateFormatter الجديد أولاً
    if (typeof DateFormatter !== 'undefined') {
      return DateFormatter.formatDate(date);
    }

    // التوافق مع DateUtils القديم
    if (typeof DateUtils !== 'undefined') {
      return DateUtils.formatDateArabic(date);
    }

    // Fallback بسيط
    if (!date) return '-';

    try {
      // استخدام Day.js إذا كان متاحاً
      if (typeof dayjs !== 'undefined') {
        const d = dayjs(date);
        return d.isValid() ? d.format('DD/MM/YYYY') : '-';
      }

      // Fallback للمتصفحات القديمة
      const d = new Date(date);
      return d.toLocaleDateString(CONFIG.DATES.LOCALE);
    } catch (error) {
      return '-';
    }
  },
  
  // تنسيق الأرقام
  formatNumber(number) {
    if (!number && number !== 0) return '-';
    return new Intl.NumberFormat(CONFIG.DATES.LOCALE).format(number);
  },
  
  // عرض القيم مع معالجة null
  displayValue(value) {
    return value === null || value === undefined || value === '' ? '-' : value;
  }
};

// متغيرات عامة مشتركة موحدة
let GlobalState = {
  // حالة الخادم
  serverRunning: false,

  // بيانات الموظفين المحملة
  allEmployees: [],
  filteredEmployees: [],
  employees: [], // متغير موحد للموظفين

  // بيانات أخرى مشتركة
  departments: [],
  jobTitles: [],

  // بيانات النماذج المختلفة
  idealEmployees: [],
  extraHours: [],
  contributions: [],
  vacations: [],
  evaluations: [],
  custody: [],
  salaryAdvances: [],
  rewardsDeductions: [],
  training: [],
  resignations: [],

  // حالة التطبيق
  currentUser: null,
  currentPage: 'index',
  currentEditId: null,

  // إعدادات العرض
  viewMode: 'table', // table, cards
  sortBy: 'code',
  sortOrder: 'asc'
};

// دوال إدارة الحالة العامة
const StateManager = {
  // تحديث حالة الخادم
  setServerStatus(status) {
    GlobalState.serverRunning = status;
    this.notifyStateChange('serverStatus', status);
  },
  
  // تحديث بيانات الموظفين
  setEmployees(employees) {
    GlobalState.allEmployees = employees;
    GlobalState.filteredEmployees = employees;
    this.updateDepartmentsAndJobs(employees);
    this.notifyStateChange('employees', employees);
  },
  
  // تحديث قوائم الإدارات والوظائف
  updateDepartmentsAndJobs(employees) {
    GlobalState.departments = [...new Set(employees.map(emp => emp.department).filter(Boolean))].sort();
    GlobalState.jobTitles = [...new Set(employees.map(emp => emp.job_title).filter(Boolean))].sort();
  },
  
  // إشعار بتغيير الحالة
  notifyStateChange(type, data) {
    // يمكن توسيع هذا لاحقاً لنظام events
    console.log(`State changed: ${type}`, data);
    
    // إرسال حدث مخصص
    window.dispatchEvent(new CustomEvent('stateChange', {
      detail: { type, data }
    }));
  },
  
  // الحصول على الحالة الحالية
  getState() {
    return { ...GlobalState };
  },
  
  // إعادة تعيين الحالة
  resetState() {
    GlobalState.allEmployees = [];
    GlobalState.filteredEmployees = [];
    GlobalState.departments = [];
    GlobalState.jobTitles = [];
    GlobalState.serverRunning = false;
  }
};

// تصدير الكائنات للاستخدام العام
if (typeof window !== 'undefined') {
  window.CONFIG = CONFIG;
  window.ConfigUtils = ConfigUtils;
  window.GlobalState = GlobalState;
  window.StateManager = StateManager;
}

// للاستخدام في Node.js
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    CONFIG,
    ConfigUtils,
    GlobalState,
    StateManager
  };
}
