/**
 * سكريپت لإزالة صلاحيات استرجاع العهد من جميع المستخدمين
 */

const { pool } = require('../config/database');

async function removeReturnCustodyPermissions() {
    try {
        console.log('🔧 بدء إزالة صلاحيات استرجاع العهد...\n');

        // الحصول على جميع المستخدمين
        const [users] = await pool.promise().query("SELECT id, username, permissions FROM users");
        
        let updatedUsers = 0;
        let totalUsers = users.length;

        console.log(`📊 تم العثور على ${totalUsers} مستخدم للفحص\n`);

        for (const user of users) {
            let permissions = {};
            let hasReturnCustodyPermissions = false;
            
            // تحليل الصلاحيات الحالية
            if (user.permissions) {
                try {
                    permissions = typeof user.permissions === 'string' 
                        ? JSON.parse(user.permissions) 
                        : user.permissions;
                } catch (parseError) {
                    console.log(`⚠️ خطأ في تحليل صلاحيات المستخدم ${user.username}:`, parseError.message);
                    continue;
                }
            }

            // فحص وجود صلاحيات استرجاع العهد
            const returnCustodyPermissions = [
                'edit_return_custody',
                'delete_return_custody'
            ];

            returnCustodyPermissions.forEach(permission => {
                if (permissions[permission]) {
                    hasReturnCustodyPermissions = true;
                    delete permissions[permission];
                    console.log(`   ❌ إزالة صلاحية ${permission} من المستخدم ${user.username}`);
                }
            });

            // تحديث المستخدم إذا كان لديه صلاحيات استرجاع العهد
            if (hasReturnCustodyPermissions) {
                try {
                    await pool.promise().query(
                        "UPDATE users SET permissions = ? WHERE id = ?",
                        [JSON.stringify(permissions), user.id]
                    );
                    
                    updatedUsers++;
                    console.log(`✅ تم تحديث صلاحيات المستخدم ${user.username}`);
                } catch (updateError) {
                    console.error(`❌ خطأ في تحديث المستخدم ${user.username}:`, updateError.message);
                }
            } else {
                console.log(`ℹ️ المستخدم ${user.username} لا يملك صلاحيات استرجاع العهد`);
            }
        }

        console.log('\n📈 إحصائيات الإزالة:');
        console.log(`   - إجمالي المستخدمين: ${totalUsers}`);
        console.log(`   - المستخدمين المحدثين: ${updatedUsers}`);
        console.log(`   - المستخدمين غير المتأثرين: ${totalUsers - updatedUsers}`);

        // إزالة الصلاحيات من جدول permissions إذا كان موجوداً
        try {
            const [permissionsTable] = await pool.promise().query("SHOW TABLES LIKE 'permissions'");
            
            if (permissionsTable.length > 0) {
                console.log('\n🗑️ إزالة صلاحيات استرجاع العهد من جدول permissions...');
                
                const [deleteResult1] = await pool.promise().query(
                    "DELETE FROM permissions WHERE name = 'edit_return_custody'"
                );
                
                const [deleteResult2] = await pool.promise().query(
                    "DELETE FROM permissions WHERE name = 'delete_return_custody'"
                );
                
                console.log(`✅ تم حذف ${deleteResult1.affectedRows + deleteResult2.affectedRows} صلاحية من جدول permissions`);
            }
        } catch (permissionsError) {
            console.log('ℹ️ جدول permissions غير موجود أو حدث خطأ:', permissionsError.message);
        }

        if (updatedUsers > 0) {
            console.log('\n🎉 تم إزالة صلاحيات استرجاع العهد بنجاح!');
            console.log('💡 الصلاحيات المحذوفة:');
            console.log('   - edit_return_custody (تعديل استرجاع العهدة)');
            console.log('   - delete_return_custody (حذف استرجاع العهدة)');
        } else {
            console.log('\n✨ لم يتم العثور على صلاحيات استرجاع العهد في أي مستخدم.');
        }

        console.log('\n🚀 انتهت عملية إزالة صلاحيات استرجاع العهد!');

    } catch (error) {
        console.error('❌ خطأ في إزالة صلاحيات استرجاع العهد:', error);
    }
}

// تشغيل السكريپت
if (require.main === module) {
    removeReturnCustodyPermissions().then(() => {
        process.exit(0);
    }).catch(error => {
        console.error('خطأ:', error);
        process.exit(1);
    });
}

module.exports = { removeReturnCustodyPermissions };
