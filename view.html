<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>عرض بيانات الموظف</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <link rel="stylesheet" href="no-sidebar-layout.css">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
      min-height: 100vh;
      padding: 20px;
      direction: rtl;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding-top: 60px; /* مساحة لزر العودة */
    }

    .header {
      background: white;
      border-radius: 15px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      color: #333;
      font-size: 28px;
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .back-btn {
      background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
      color: white;
      border: none;
      padding: 12px 25px;
      border-radius: 25px;
      cursor: pointer;
      font-size: 16px;
      transition: all 0.3s ease;
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .back-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(33, 150, 243, 0.4);
    }

    .employee-profile {
      background: white;
      border-radius: 20px;
      overflow: hidden;
      box-shadow: 0 15px 35px rgba(0,0,0,0.1);
      margin-bottom: 30px;
    }

    .profile-header {
      background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
      padding: 30px;
      color: white;
      text-align: center;
      position: relative;
    }

    .profile-photo {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      border: 5px solid white;
      margin: 0 auto 20px;
      overflow: hidden;
      background: rgba(255,255,255,0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
    }

    .profile-photo img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .profile-photo .no-photo {
      font-size: 40px;
      color: rgba(255,255,255,0.7);
    }

    .profile-info h2 {
      font-size: 24px;
      margin-bottom: 10px;
    }

    .profile-info p {
      font-size: 16px;
      opacity: 0.9;
    }

    .profile-actions {
      position: absolute;
      top: 20px;
      left: 20px;
      display: flex;
      gap: 10px;
    }

    .action-btn {
      background: rgba(255,255,255,0.2);
      border: 1px solid rgba(255,255,255,0.3);
      color: white;
      padding: 8px 12px;
      border-radius: 20px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s ease;
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .action-btn:hover {
      background: rgba(255,255,255,0.3);
      transform: translateY(-1px);
    }

    .content-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30px;
      margin-bottom: 30px;
    }

    .main-content {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .sidebar {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .info-card {
      background: white;
      border-radius: 15px;
      padding: 25px;
      box-shadow: 0 5px 20px rgba(0,0,0,0.08);
      transition: all 0.3s ease;
    }

    .info-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 30px rgba(0,0,0,0.12);
    }

    .card-title {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 2px solid #f0f0f0;
    }

    .card-title i {
      font-size: 20px;
      color: #2196F3;
    }

    .card-title h3 {
      color: #333;
      font-size: 18px;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 15px;
    }

    .info-item {
      display: flex;
      flex-direction: column;
      gap: 5px;
    }

    .info-label {
      font-size: 12px;
      color: #666;
      font-weight: 600;
      text-transform: uppercase;
    }

    .info-value {
      font-size: 14px;
      color: #333;
      font-weight: 500;
      padding: 8px 12px;
      background: #f8f9fa;
      border-radius: 8px;
      border-right: 3px solid #2196F3;
    }

    .documents-section {
      background: white;
      border-radius: 15px;
      padding: 25px;
      box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    }

    .documents-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 2px solid #f0f0f0;
    }

    .upload-btn {
      background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 20px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .upload-btn:hover {
      transform: translateY(-1px);
      box-shadow: 0 5px 15px rgba(33, 150, 243, 0.3);
    }

    .documents-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 15px;
    }

    .document-card {
      border: 2px solid #f0f0f0;
      border-radius: 12px;
      padding: 15px;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .document-card:hover {
      border-color: #2196F3;
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .document-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 10px;
    }

    .file-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      color: white;
    }

    .file-icon.pdf { background: #dc3545; }
    .file-icon.word { background: #007bff; }
    .file-icon.excel { background: #28a745; }
    .file-icon.image { background: #6f42c1; }
    .file-icon.text { background: #6c757d; }

    .document-info h4 {
      font-size: 14px;
      color: #333;
      margin-bottom: 5px;
    }

    .document-meta {
      font-size: 12px;
      color: #666;
    }

    .document-actions {
      display: flex;
      gap: 8px;
      margin-top: 10px;
    }

    .doc-btn {
      flex: 1;
      padding: 6px 12px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.3s ease;
      text-decoration: none;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 5px;
    }

    .view-btn {
      background: #007bff;
      color: white;
    }

    .download-btn {
      background: #28a745;
      color: white;
    }

    .delete-btn {
      background: #dc3545;
      color: white;
    }

    .doc-btn:hover {
      transform: translateY(-1px);
      opacity: 0.9;
    }

    .no-documents {
      text-align: center;
      padding: 40px;
      color: #666;
    }

    .no-documents i {
      font-size: 48px;
      margin-bottom: 15px;
      opacity: 0.5;
    }

    @media (max-width: 768px) {
      .content-grid {
        grid-template-columns: 1fr;
      }

      .info-grid {
        grid-template-columns: 1fr;
      }

      .documents-grid {
        grid-template-columns: 1fr;
      }

      .profile-actions {
        position: static;
        justify-content: center;
        margin-top: 20px;
      }
    }

    /* تنسيقات الطباعة */
    @media print {
      body {
        background: white !important;
        padding: 0 !important;
        margin: 0 !important;
      }

      .header {
        background: white !important;
        box-shadow: none !important;
        border-bottom: 2px solid #333 !important;
        margin-bottom: 20px !important;
      }

      .back-btn {
        display: none !important;
      }

      .profile-header {
        background: white !important;
        color: #333 !important;
        border: 2px solid #333 !important;
      }

      .profile-actions {
        display: none !important;
      }

      .content-grid {
        grid-template-columns: 1fr 1fr !important;
        gap: 20px !important;
      }

      .info-card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
        page-break-inside: avoid;
      }

      .card-title {
        background: #f5f5f5 !important;
        padding: 10px !important;
        margin: -25px -25px 20px -25px !important;
      }

      .card-title i {
        color: #333 !important;
      }

      .info-value {
        background: white !important;
        border: 1px solid #ddd !important;
        border-right: 3px solid #333 !important;
      }

      .documents-section {
        page-break-before: always;
        box-shadow: none !important;
        border: 1px solid #ddd !important;
      }

      .documents-header {
        background: #f5f5f5 !important;
        padding: 10px !important;
        margin: -25px -25px 20px -25px !important;
      }

      .document-card {
        border: 1px solid #ddd !important;
        page-break-inside: avoid;
      }

      .doc-btn {
        display: none !important;
      }

      .file-icon {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
      }

      @page {
        size: A4;
        margin: 2cm;
      }
    }
  </style>

    <!-- تحميل Day.js للتواريخ -->
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/dayjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/plugin/utc.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/plugin/timezone.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/plugin/customParseFormat.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/plugin/localizedFormat.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/locale/ar.js"></script>
    
    <!-- تحميل DateFormatter الموحد -->
    <script src="utils/dateFormatter-browser.js"></script>
    
    <script>
        // تهيئة Day.js
        if (typeof dayjs !== 'undefined') {
            dayjs.extend(dayjs_plugin_utc);
            dayjs.extend(dayjs_plugin_timezone);
            dayjs.extend(dayjs_plugin_customParseFormat);
            dayjs.extend(dayjs_plugin_localizedFormat);
            dayjs.locale('ar');
            console.log('DateFormatter تم تحميله بنجاح');
        }
    </script>
</head>
<body>
  <div class="container">
    <!-- Header -->
    <div class="header">
      <h1>
        <i class="fas fa-user-circle"></i>
        بيانات الموظف
      </h1>
      <a href="dashboard.html" class="back-btn">
        <i class="fas fa-arrow-right"></i>
        العودة للوحة التحكم
      </a>
    </div>

    <!-- Employee Profile -->
    <div class="employee-profile">
      <div class="profile-header">
        <div class="profile-actions">
          <a href="#" class="action-btn" id="editBtn">
            <i class="fas fa-edit"></i>
            تعديل
          </a>
          <button class="action-btn" onclick="window.print()">
            <i class="fas fa-print"></i>
            طباعة
          </button>
        </div>

        <div class="profile-photo" id="profilePhoto">
          <i class="fas fa-user no-photo"></i>
        </div>

        <div class="profile-info">
          <h2 id="employeeName">اسم الموظف</h2>
          <p id="employeeJob">المسمى الوظيفي</p>
        </div>
      </div>
    </div>

    <!-- Content Grid -->
    <div class="content-grid">
      <!-- Left Column -->
      <div class="main-content">
        <!-- Personal Information -->
        <div class="info-card">
          <div class="card-title">
            <i class="fas fa-user"></i>
            <h3>البيانات الشخصية</h3>
          </div>
          <div class="info-grid" id="personalInfo">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- Insurance Information -->
        <div class="info-card">
          <div class="card-title">
            <i class="fas fa-shield-alt"></i>
            <h3>بيانات التأمين</h3>
          </div>
          <div class="info-grid" id="insuranceInfo">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- Leave Information -->
        <div class="info-card">
          <div class="card-title">
            <i class="fas fa-calendar-alt"></i>
            <h3>بيانات الإجازات</h3>
          </div>
          <div class="info-grid" id="leaveInfo">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>
      </div>

      <!-- Right Column -->
      <div class="main-content">
        <!-- Job Information -->
        <div class="info-card">
          <div class="card-title">
            <i class="fas fa-briefcase"></i>
            <h3>بيانات العمل</h3>
          </div>
          <div class="info-grid" id="jobInfo">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- Skills Information -->
        <div class="info-card">
          <div class="card-title">
            <i class="fas fa-tools"></i>
            <h3>بيانات المهارات</h3>
          </div>
          <div class="info-grid" id="skillsInfo">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- Quick Stats -->
        <div class="info-card">
          <div class="card-title">
            <i class="fas fa-chart-bar"></i>
            <h3>إحصائيات سريعة</h3>
          </div>
          <div id="quickStats">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>
      </div>
    </div>



    <!-- Documents Section -->
    <div class="documents-section">
      <div class="documents-header">
        <div class="card-title" style="margin-bottom: 0; padding-bottom: 0; border-bottom: none;">
          <i class="fas fa-folder-open"></i>
          <h3>مستندات الموظف</h3>
        </div>
        <span id="documentsCount" class="badge">0 مستند</span>
      </div>

      <div class="documents-grid" id="documentsGrid">
        <!-- Will be populated by JavaScript -->
      </div>

      <div class="no-documents" id="noDocuments" style="display: none;">
        <i class="fas fa-folder-open"></i>
        <h4>لا توجد مستندات</h4>
        <p>لم يتم رفع أي مستندات لهذا الموظف بعد</p>
      </div>
    </div>
  </div>

  <script src="dateUtils.js"></script>
  <script>
    const API_URL = 'http://localhost:5500/api';
    let currentEmployeeCode = null;

    // الحصول على كود الموظف من URL
    const urlParams = new URLSearchParams(window.location.search);
    currentEmployeeCode = urlParams.get('code');

    if (!currentEmployeeCode) {
      alert('لم يتم تحديد كود الموظف');
      window.location.href = 'dashboard.html';
    }

    // تحميل بيانات الموظف عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', async function() {
      await loadEmployeeData();
      await loadEmployeePhoto();
      await loadEmployeeDocuments();


    });

    // تحميل بيانات الموظف
    async function loadEmployeeData() {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/employees/${currentEmployeeCode}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        if (!response.ok) throw new Error('فشل في جلب البيانات');

        const employee = await response.json();

        // تحميل رصيد الإجازات المحدث من الخادم
        try {
          const token = localStorage.getItem('token');
          const leaveResponse = await fetch(`${API_URL}/employees/${currentEmployeeCode}/leave-balance`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          if (leaveResponse.ok) {
            const leaveData = await leaveResponse.json();
            // تحديث بيانات الموظف برصيد الإجازات المحسوب
            employee.calculated_leave_balance = leaveData.calculated_balance;
            employee.calculated_leave_used = leaveData.used_days;
            employee.calculated_leave_remaining = leaveData.remaining_days;
            console.log('تم تحميل رصيد الإجازات من الخادم:', leaveData);
          }
        } catch (leaveError) {
          console.warn('تعذر تحميل رصيد الإجازات من الخادم، سيتم الحساب محلياً:', leaveError);
        }

        displayEmployeeData(employee);
      } catch (error) {
        console.error('خطأ في تحميل بيانات الموظف:', error);
        alert('فشل في تحميل بيانات الموظف');
      }
    }

    // عرض بيانات الموظف
    function displayEmployeeData(employee) {
      // تحديث معلومات الملف الشخصي
      document.getElementById('employeeName').textContent = employee.full_name || 'غير محدد';
      document.getElementById('employeeJob').textContent = employee.job_title || 'غير محدد';
      document.getElementById('editBtn').href = `edit.html?code=${employee.code}`;

      // البيانات الشخصية
      const personalInfo = document.getElementById('personalInfo');
      personalInfo.innerHTML = createInfoItems([
        { label: 'كود الموظف', value: employee.code },
        { label: 'الاسم الكامل', value: employee.full_name },
        { label: 'تاريخ الميلاد', value: employee.birth_date, isDate: true },
        { label: 'الرقم القومي', value: employee.national_id },
        { label: 'العنوان', value: employee.address },
        { label: 'الهاتف', value: employee.phone },
        { label: 'الحالة الاجتماعية', value: employee.marital_status },
        { label: 'عدد الأطفال', value: employee.children },
        { label: 'المؤهل', value: employee.qualification },
        { label: 'الاحتياجات الخاصة', value: employee.special_needs }
      ]);

      // بيانات العمل
      const jobInfo = document.getElementById('jobInfo');
      jobInfo.innerHTML = createInfoItems([
        { label: 'القسم', value: employee.department },
        { label: 'المسمى الوظيفي', value: employee.job_title },
        { label: 'تاريخ التعيين', value: employee.hire_date, isDate: true },
        { label: 'الراتب الإجمالي', value: employee.total_salary },
        { label: 'تكلفة العامل', value: employee.worker_cost },
        { label: 'تكلفة الشركة', value: employee.company_cost },
        { label: 'الحالة', value: employee.status }
      ]);

      // بيانات التأمين
      const insuranceInfo = document.getElementById('insuranceInfo');
      insuranceInfo.innerHTML = createInfoItems([
        { label: 'التأمين الاجتماعي', value: employee.social_insurance },
        { label: 'رقم التأمين', value: employee.insurance_number },
        { label: 'جهة التأمين', value: employee.insurance_entity },
        { label: 'تاريخ بداية التأمين', value: employee.insurance_start, isDate: true },
        { label: 'وظيفة التأمين', value: employee.insurance_job },
        { label: 'راتب التأمين', value: employee.insurance_salary },
        { label: 'البطاقة الصحية', value: employee.health_card }
      ]);

      // بيانات المهارات
      const skillsInfo = document.getElementById('skillsInfo');
      skillsInfo.innerHTML = createInfoItems([
        { label: 'مستوى المهارة', value: employee.skill_level },
        { label: 'تاريخ بداية المهارة', value: employee.skill_start, isDate: true },
        { label: 'تاريخ انتهاء المهارة', value: employee.skill_end, isDate: true },
        { label: 'المدة المتبقية', value: employee.skill_remaining },
        { label: 'وظيفة المهارة', value: employee.skill_job }
      ]);

      // استخدام القيم المحسوبة من الخادم إذا كانت متاحة، وإلا احسبها محلياً
      let leaveBalance, leaveUsed, leaveRemaining;

      if (employee.calculated_leave_balance !== undefined) {
        // استخدام القيم المحسوبة من الخادم
        leaveBalance = employee.calculated_leave_balance;
        leaveUsed = employee.calculated_leave_used;
        leaveRemaining = employee.calculated_leave_remaining;
        console.log('استخدام رصيد الإجازات من الخادم:', { leaveBalance, leaveUsed, leaveRemaining });
      } else {
        // حساب رصيد الإجازات محلياً كـ fallback
        const calculatedLeave = calculateLeaveBalance(employee.hire_date);
        leaveBalance = employee.leave_balance && employee.leave_balance > 0 ? employee.leave_balance : calculatedLeave.balance;
        leaveUsed = employee.leave_used || 0;
        leaveRemaining = employee.leave_remaining && employee.leave_remaining >= 0 ? employee.leave_remaining : (leaveBalance - leaveUsed);
        console.log('استخدام رصيد الإجازات المحسوب محلياً:', { leaveBalance, leaveUsed, leaveRemaining });
      }

      // بيانات الإجازات
      const leaveInfo = document.getElementById('leaveInfo');
      leaveInfo.innerHTML = createInfoItems([
        { label: 'رصيد الإجازات', value: leaveBalance },
        { label: 'الإجازات المستخدمة', value: leaveUsed },
        { label: 'الإجازات المتبقية', value: leaveRemaining }
      ]);

      // الإحصائيات السريعة
      const quickStats = document.getElementById('quickStats');
      const servicePeriod = calculateServicePeriod(employee.hire_date);
      const serviceYears = calculateServiceYearsOnly(employee.hire_date);

      quickStats.innerHTML = `
        <div class="info-item">
          <div class="info-label">رصيد الإجازات</div>
          <div class="info-value">${leaveBalance} يوم</div>
        </div>
        <div class="info-item">
          <div class="info-label">الإجازات المستخدمة</div>
          <div class="info-value">${leaveUsed} يوم</div>
        </div>
        <div class="info-item">
          <div class="info-label">الإجازات المتبقية</div>
          <div class="info-value">${leaveRemaining} يوم</div>
        </div>
        <div class="info-item">
          <div class="info-label">مدة الخدمة</div>
          <div class="info-value">${servicePeriod}</div>
        </div>
        <div class="info-item">
          <div class="info-label">سنوات الخبرة</div>
          <div class="info-value">${serviceYears} سنة</div>
        </div>
      `;
    }

    // إنشاء عناصر المعلومات
    function createInfoItems(items) {
      return items.map(item => {
        let displayValue = item.value || 'غير محدد';

        // تنسيق التواريخ تلقائياً
        if (item.isDate && item.value) {
          displayValue = formatDate(item.value);
        }

        return `
          <div class="info-item">
            <div class="info-label">${item.label}</div>
            <div class="info-value">${displayValue}</div>
          </div>
        `;
      }).join('');
    }

    // حساب مدة الخدمة بالتفصيل
    function calculateServicePeriod(hireDate) {
      return DateUtils.calculateServiceDuration(hireDate);
    }

    // حساب رصيد الإجازات بناءً على تاريخ التعيين (نفس المعادلة الموجودة في الخادم)
    function calculateLeaveBalance(hireDate) {
      if (!hireDate) {
        return { balance: 7, used: 0, remaining: 7 }; // قيمة افتراضية
      }

      try {
        let hire;

        // معالجة التاريخ بنفس طريقة مكتبة التواريخ
        if (typeof hireDate === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(hireDate)) {
          const [year, month, day] = hireDate.split('-');
          hire = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
        } else if (hireDate instanceof Date) {
          const egyptTime = new Date(hireDate.toLocaleString("en-US", {timeZone: "Africa/Cairo"}));
          hire = new Date(egyptTime.getFullYear(), egyptTime.getMonth(), egyptTime.getDate());
        } else if (typeof hireDate === 'string' && hireDate.includes('T')) {
          const date = new Date(hireDate);
          const egyptTime = new Date(date.toLocaleString("en-US", {timeZone: "Africa/Cairo"}));
          hire = new Date(egyptTime.getFullYear(), egyptTime.getMonth(), egyptTime.getDate());
        } else {
          hire = new Date(hireDate);
        }

        if (!hire || isNaN(hire.getTime())) {
          return { balance: 7, used: 0, remaining: 7 }; // قيمة افتراضية للتواريخ غير الصحيحة
        }

        // تحديد تاريخ المرجع (25 يونيو من العام الحالي)
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const referenceDate = new Date(currentYear, 5, 25); // 25 يونيو

        // إذا كان التاريخ الحالي قبل 25 يونيو، استخدم 25 يونيو من العام السابق
        if (currentDate < referenceDate) {
          referenceDate.setFullYear(currentYear - 1);
        }

        // التأكد من أن تاريخ التعيين ليس في المستقبل
        if (hire > currentDate) {
          return { balance: 7, used: 0, remaining: 7 };
        }

        // حساب الفترة بين تاريخ التعيين وتاريخ المرجع
        const yearDiff = referenceDate.getFullYear() - hire.getFullYear();
        const monthDiff = referenceDate.getMonth() - hire.getMonth();
        const dayDiff = referenceDate.getDate() - hire.getDate();

        // حساب المدة الدقيقة بالسنوات
        let yearsOfService = yearDiff;
        if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
          yearsOfService--;
        }

        // حساب المدة بالأشهر للموظفين الجدد
        const totalMonths = yearDiff * 12 + monthDiff;
        const monthsOfService = totalMonths >= 0 ? totalMonths : 0;

        // تحديد رصيد الإجازات بناءً على مدة الخدمة (نفس المعادلة في الخادم)
        let leaveBalance = 0;

        if (monthsOfService < 6) {
          // أقل من 6 أشهر: 7 أيام
          leaveBalance = 7;
        } else if (yearsOfService < 10) {
          // من 6 أشهر إلى 10 سنوات: 21 يوم
          leaveBalance = 21;
        } else {
          // أكثر من 10 سنوات: 30 يوم
          leaveBalance = 30;
        }

        return {
          balance: leaveBalance,
          used: 0, // سيتم حسابه من قاعدة البيانات
          remaining: leaveBalance,
          yearsOfService: yearsOfService,
          monthsOfService: monthsOfService
        };

      } catch (error) {
        console.error('خطأ في حساب رصيد الإجازات:', error);
        return { balance: 7, used: 0, remaining: 7 };
      }
    }

    // حساب سنوات الخدمة فقط (للإحصائيات)
    function calculateServiceYearsOnly(hireDate) {
      if (!hireDate) return 0;

      const hire = new Date(hireDate);
      const now = new Date();

      // التحقق من صحة التاريخ
      if (isNaN(hire.getTime()) || hire > now) {
        return 0;
      }

      const years = now.getFullYear() - hire.getFullYear();
      const monthDiff = now.getMonth() - hire.getMonth();
      const dayDiff = now.getDate() - hire.getDate();

      // تعديل السنوات إذا لم يكتمل العام الحالي
      if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
        return Math.max(0, years - 1);
      }

      return Math.max(0, years);
    }

    // تحميل صورة الموظف
    async function loadEmployeePhoto() {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/employees/${currentEmployeeCode}/photo`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        if (response.ok) {
          const photo = await response.json();
          const profilePhoto = document.getElementById('profilePhoto');
          profilePhoto.innerHTML = `<img src="${API_URL.replace('/api', '')}/api/files/${photo.photo_path}" alt="صورة الموظف">`;
        }
      } catch (error) {
        console.log('لا توجد صورة للموظف');
      }
    }

    // تحميل مستندات الموظف
    async function loadEmployeeDocuments() {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/employees/${currentEmployeeCode}/documents`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        if (response.ok) {
          const documents = await response.json();
          displayDocuments(documents);
        } else {
          showNoDocuments();
        }
      } catch (error) {
        console.log('لا توجد مستندات للموظف');
        showNoDocuments();
      }
    }

    // عرض المستندات
    function displayDocuments(documents) {
      const documentsGrid = document.getElementById('documentsGrid');
      const noDocuments = document.getElementById('noDocuments');
      const documentsCount = document.getElementById('documentsCount');

      if (documents.length === 0) {
        showNoDocuments();
        return;
      }

      documentsCount.textContent = `${documents.length} مستند`;
      noDocuments.style.display = 'none';
      documentsGrid.style.display = 'grid';

      documentsGrid.innerHTML = documents.map(doc => `
        <div class="document-card">
          <div class="document-header">
            <div class="file-icon ${getFileType(doc.file_extension)}">
              <i class="${getFileIcon(doc.file_extension)}"></i>
            </div>
            <div class="document-info">
              <h4>${doc.display_name}</h4>
              <div class="document-meta">
                ${formatFileSize(doc.file_size)} • ${formatDate(doc.uploaded_at)}
              </div>
            </div>
          </div>
          <div class="document-actions">
            <a href="${API_URL.replace('/api', '')}/api/files/${doc.document_path}"
               target="_blank" class="doc-btn view-btn">
              <i class="fas fa-eye"></i> عرض
            </a>
            <a href="${API_URL}/download/employees/${currentEmployeeCode}/documents/${doc.id}"
               class="doc-btn download-btn" download>
              <i class="fas fa-download"></i> تحميل
            </a>
          </div>
        </div>
      `).join('');
    }

    // عرض رسالة عدم وجود مستندات
    function showNoDocuments() {
      const documentsGrid = document.getElementById('documentsGrid');
      const noDocuments = document.getElementById('noDocuments');
      const documentsCount = document.getElementById('documentsCount');

      documentsCount.textContent = '0 مستند';
      documentsGrid.style.display = 'none';
      noDocuments.style.display = 'block';
    }

    // الحصول على نوع الملف
    function getFileType(extension) {
      const ext = extension.toLowerCase();
      if (ext === '.pdf') return 'pdf';
      if (['.doc', '.docx'].includes(ext)) return 'word';
      if (['.xls', '.xlsx'].includes(ext)) return 'excel';
      if (['.jpg', '.jpeg', '.png'].includes(ext)) return 'image';
      if (ext === '.txt') return 'text';
      return 'text';
    }

    // الحصول على أيقونة الملف
    function getFileIcon(extension) {
      const ext = extension.toLowerCase();
      if (ext === '.pdf') return 'fas fa-file-pdf';
      if (['.doc', '.docx'].includes(ext)) return 'fas fa-file-word';
      if (['.xls', '.xlsx'].includes(ext)) return 'fas fa-file-excel';
      if (['.jpg', '.jpeg', '.png'].includes(ext)) return 'fas fa-file-image';
      if (ext === '.txt') return 'fas fa-file-alt';
      return 'fas fa-file';
    }

    // تنسيق حجم الملف
    function formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // تنسيق التاريخ - محدث لاستخدام DateFormatter
    function formatDate(dateString) {
      // استخدام DateFormatter الجديد أولاً
      if (typeof DateFormatter !== 'undefined') {
        return DateFormatter.formatDateFromDatabase(dateString);
      }

      // التوافق مع DateUtils القديم
      if (typeof DateUtils !== 'undefined') {
        return DateUtils.formatDateFromDatabase(dateString);
      }

      return dateString || '-';
    }

    // تنسيق التاريخ المختصر - محدث لاستخدام DateFormatter
    function formatDateShort(dateString) {
      // استخدام DateFormatter الجديد أولاً
      if (typeof DateFormatter !== 'undefined') {
        return DateFormatter.formatDate(dateString);
      }

      // التوافق مع DateUtils القديم
      if (typeof DateUtils !== 'undefined') {
        return DateUtils.formatDateFromDatabase(dateString);
      }

      return dateString || '-';
    }


  </script>
</body>
</html>
