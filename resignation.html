<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>قسم الاستقالات</title>
  <link rel="stylesheet" href="style.css" />
  <link rel="stylesheet" href="resignation.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="no-sidebar-layout.css">
  <script src="permissions.js" defer></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <!-- تحميل Day.js للتواريخ -->
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/dayjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/plugin/utc.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/plugin/timezone.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/plugin/customParseFormat.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/plugin/localizedFormat.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/locale/ar.js"></script>
    
    <!-- تحميل DateFormatter الموحد -->
    <script src="utils/dateFormatter-browser.js"></script>
    
    <script>
        // تهيئة Day.js
        if (typeof dayjs !== 'undefined') {
            dayjs.extend(dayjs_plugin_utc);
            dayjs.extend(dayjs_plugin_timezone);
            dayjs.extend(dayjs_plugin_customParseFormat);
            dayjs.extend(dayjs_plugin_localizedFormat);
            dayjs.locale('ar');
            console.log('DateFormatter تم تحميله بنجاح');
        }
    </script>
</head>
<body class="resignation-page">

    <!-- زر العودة للوحة التحكم -->
  <div class="back-to-dashboard">
    <a href="resignation-cards.html" class="dashboard-btn">
      <i class="fas fa-arrow-right"></i>
      <span>العودة لإدارة الاستقالات</span>
    </a>
  </div>




  <div class="main-content full-width" id="mainContent">
    <h1>قسم الاستقالات</h1>

    
    <!-- تبويب إضافة استقالة -->
    <div class="tab-content" id="add-resignation" style="display: none;">
      <div class="resignation-form">
        <div class="form-group">
          <label for="resignationEmployeeSearch">البحث عن الموظف (بالاسم أو الكود):</label>
          <input type="text" id="resignationEmployeeSearch" class="employee-search-input" placeholder="أدخل اسم أو كود الموظف" list="resignationEmployeeSearchSuggestions" autocomplete="off">
          <datalist id="resignationEmployeeSearchSuggestions"></datalist>
        </div>
        <div class="form-group">
          <label for="resignationEmployeeCode">كود الموظف:</label>
          <input type="text" id="resignationEmployeeCode" readonly>
        </div>
        <div class="form-group">
          <label for="resignationEmployeeName">اسم الموظف:</label>
          <input type="text" id="resignationEmployeeName" readonly>
        </div>
        <div class="form-group">
          <label for="resignationEmployeeDepartment">الإدارة:</label>
          <input type="text" id="resignationEmployeeDepartment" readonly>
        </div>
        <div class="form-group">
          <label for="resignationReason">سبب الاستقالة:</label>
          <input type="text" id="resignationReason" placeholder="أدخل سبب الاستقالة" required>
        </div>
        <div class="form-group">
          <label for="resignationDate">تاريخ الاستقالة:</label>
          <input type="date" id="resignationDate" required>
        </div>
        <div class="form-group">
          <label for="workEndDate">تاريخ ترك العمل:</label>
          <input type="date" id="workEndDate">
        </div>
        <div class="form-group">
          <label for="resignationRecommendation">التوصية:</label>
          <select id="resignationRecommendation" required>
            <option value="">اختر التوصية</option>
            <option value="يوصى بعودته">يوصى بعودته</option>
            <option value="لا يوصى بعودته">لا يوصى بعودته</option>
          </select>
        </div>
        <div class="form-group">
          <label for="resignationNotes">ملاحظات:</label>
          <textarea id="resignationNotes" rows="3" placeholder="ملاحظات إضافية (اختياري)"></textarea>
        </div>
        <div class="form-actions">
          <button id="saveResignation" class="save-btn">حفظ الاستقالة</button>
          <button id="resetResignationForm" class="reset-btn">إعادة تعيين</button>
        </div>
      </div>
      
      <!-- الجدول الديناميكي -->
      <div class="resignation-table-container">
        <h3>قائمة الاستقالات</h3>

        <!-- فلاتر البحث المحددة -->
        <div class="filtered-search-container">
          <div class="search-filters-row">
            <div class="form-group">
              <label for="filterResignationEmployeeCode">كود الموظف:</label>
              <input type="text" id="filterResignationEmployeeCode" placeholder="أدخل كود الموظف" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterResignationEmployeeName">اسم الموظف:</label>
              <input type="text" id="filterResignationEmployeeName" placeholder="أدخل اسم الموظف" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterResignationFromDate">من تاريخ:</label>
              <input type="date" id="filterResignationFromDate" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterResignationToDate">إلى تاريخ:</label>
              <input type="date" id="filterResignationToDate" class="filter-input">
            </div>
          </div>

          <div class="filter-actions">
            <button id="clearResignationFiltersBtn" class="reset-btn">مسح الفلاتر</button>
            <button id="exportResignationsBtn" class="export-btn">تصدير إلى Excel</button>
          </div>
        </div>
        <table class="resignation-table" id="resignation-table">
          <thead>
            <tr>
              <th>كود الموظف</th>
              <th>اسم الموظف</th>
              <th>الإدارة</th>
              <th>سبب الاستقالة</th>
              <th>تاريخ الاستقالة</th>
              <th>تاريخ ترك العمل</th>
              <th>التوصية</th>
              <th>ملاحظات</th>
              <th>حالة الموظف</th>
              <th>إجراءات</th>
              <th>بيانات الموظف</th>
            </tr>
          </thead>
          <tbody id="resignationTableBody">
            <!-- سيتم تعبئة الصفوف ديناميكياً -->
          </tbody>
        </table>
      </div>
    </div>
    
    <!-- تبويب تقارير الاستقالات -->
    <div class="tab-content" id="resignation-reports" style="display:none;">
      <div class="unified-filters">
        <div class="filter-row">
          <div class="form-group">
            <label for="reportStartDate">من تاريخ</label>
            <input type="date" id="reportStartDate">
          </div>
          <div class="form-group">
            <label for="reportEndDate">إلى تاريخ</label>
            <input type="date" id="reportEndDate">
          </div>
          <div class="form-group">
            <label for="reportDepartment">الإدارة</label>
            <select id="reportDepartment">
              <option value="">كل الإدارات</option>
            </select>
          </div>
          <div class="form-group">
            <label for="reportReason">سبب الاستقالة</label>
            <input type="text" id="reportReason" placeholder="أدخل سبب الاستقالة للبحث">
          </div>
          <div class="form-group">
            <label for="reportRecommendation">التوصية</label>
            <select id="reportRecommendation">
              <option value="">كل التوصيات</option>
              <option value="يوصى بعودته">يوصى بعودته</option>
              <option value="لا يوصى بعودته">لا يوصى بعودته</option>
            </select>
          </div>
        </div>
        <div class="filter-actions">
          <button id="applyReportFilters" class="search-btn">تطبيق الفلاتر</button>
          <button id="resetReportFilters" class="reset-btn">مسح الفلاتر</button>
        </div>
      </div>
      
      <div class="reports-container">
        <div class="report-card" id="total-resignations-report">
          <div class="report-icon"><i class="fas fa-user-minus"></i></div>
          <div class="report-value" id="totalResignationsValue">0</div>
          <div class="report-footer">إجمالي الاستقالات</div>
        </div>
        <div class="report-card" id="recommended-resignations-report">
          <div class="report-icon"><i class="fas fa-thumbs-up"></i></div>
          <div class="report-value" id="recommendedResignationsValue">0</div>
          <div class="report-footer">يوصى بعودتهم</div>
        </div>
        <div class="report-card" id="not-recommended-resignations-report">
          <div class="report-icon"><i class="fas fa-thumbs-down"></i></div>
          <div class="report-value" id="notRecommendedResignationsValue">0</div>
          <div class="report-footer">لا يوصى بعودتهم</div>
        </div>
      </div>
      
      <div class="reports-tables">
        <div class="table-controls">
          <input type="text" id="searchReportTable" placeholder="بحث في التقارير...">
          <button id="exportReportTableBtn" class="export-btn">تصدير إلى Excel</button>
          <button id="printReportBtn" class="print-btn">طباعة التقرير</button>
        </div>
        <table class="report-table" id="resignation-report-table">
          <thead>
            <tr>
              <th>كود الموظف</th>
              <th>اسم الموظف</th>
              <th class="department-column">الإدارة</th>
              <th>سبب الاستقالة</th>
              <th>تاريخ الاستقالة</th>
              <th>التوصية</th>
              <th class="notes-column">ملاحظات</th>
            </tr>
          </thead>
          <tbody>
            <!-- سيتم تعبئة الصفوف ديناميكياً -->
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- نافذة منبثقة لتعديل الاستقالة -->
  <div id="editResignationModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>تعديل الاستقالة</h2>
        <span class="close" id="closeEditResignationModal">&times;</span>
      </div>
      <div class="modal-body">
        <div class="resignation-form">
          <input type="hidden" id="editResignationId">

          <div class="form-group">
            <label for="editResignationEmployeeCode">كود الموظف:</label>
            <input type="text" id="editResignationEmployeeCode" readonly>
          </div>

          <div class="form-group">
            <label for="editResignationEmployeeName">اسم الموظف:</label>
            <input type="text" id="editResignationEmployeeName" readonly>
          </div>

          <div class="form-group">
            <label for="editResignationEmployeeDepartment">الإدارة:</label>
            <input type="text" id="editResignationEmployeeDepartment" readonly>
          </div>

          <div class="form-group">
            <label for="editResignationReason">سبب الاستقالة:</label>
            <input type="text" id="editResignationReason" placeholder="أدخل سبب الاستقالة" required>
          </div>

          <div class="form-group">
            <label for="editResignationDate">تاريخ الاستقالة:</label>
            <input type="date" id="editResignationDate" required>
          </div>

          <div class="form-group">
            <label for="editWorkEndDate">تاريخ ترك العمل:</label>
            <input type="date" id="editWorkEndDate">
          </div>

          <div class="form-group">
            <label for="editResignationRecommendation">التوصية:</label>
            <select id="editResignationRecommendation" required>
              <option value="">اختر التوصية</option>
              <option value="يوصى بعودته">يوصى بعودته</option>
              <option value="لا يوصى بعودته">لا يوصى بعودته</option>
            </select>
          </div>

          <div class="form-group"></div> <!-- حقل فارغ للتوازن -->

          <div class="form-group full-width">
            <label for="editResignationNotes">ملاحظات:</label>
            <textarea id="editResignationNotes" rows="3" placeholder="ملاحظات إضافية (اختياري)"></textarea>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button id="updateResignationBtn" class="save-btn">تحديث الاستقالة</button>
        <button class="cancel-btn" onclick="document.getElementById('editResignationModal').style.display='none'">إلغاء</button>
      </div>
    </div>
  </div>

  <script src="dateUtils.js"></script>
  <script src="form-validation.js"></script>
  <script src="resignation.js"></script>

  <script src="sidebar.js"></script>
  
</body>
</html>
