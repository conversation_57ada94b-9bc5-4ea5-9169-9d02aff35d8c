<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>قسم السلف</title>
  <link rel="stylesheet" href="style.css" />
  <link rel="stylesheet" href="salaryAdvance.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="no-sidebar-layout.css">
  <script src="permissions.js" defer></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <!-- تحميل Day.js للتواريخ -->
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/dayjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/plugin/utc.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/plugin/timezone.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/plugin/customParseFormat.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/plugin/localizedFormat.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/locale/ar.js"></script>
    
    <!-- تحميل DateFormatter الموحد -->
    <script src="utils/dateFormatter-browser.js"></script>
    
    <script>
        // تهيئة Day.js
        if (typeof dayjs !== 'undefined') {
            dayjs.extend(dayjs_plugin_utc);
            dayjs.extend(dayjs_plugin_timezone);
            dayjs.extend(dayjs_plugin_customParseFormat);
            dayjs.extend(dayjs_plugin_localizedFormat);
            dayjs.locale('ar');
            console.log('DateFormatter تم تحميله بنجاح');
        }
    </script>
</head>
<body>

    <!-- زر العودة للوحة التحكم -->
  <div class="back-to-dashboard">
    <a href="salary-advance-cards.html" class="dashboard-btn">
      <i class="fas fa-arrow-right"></i>
      <span>العودة لإدارة السلف</span>
    </a>
  </div>


<div class="main-content full-width" id="mainContent">
  <h1>قسم السلف</h1>



  <!-- محتوى إضافة سلفة جديدة -->
  <div class="tab-content" id="add-advance" style="display: none;">
    <div class="advance-form">
      <h2>إضافة سلفة جديدة</h2>
      
      <!-- البحث عن الموظف -->
      <div class="form-row">
        <div class="form-group full-width">
          <label for="employeeSearchAdd">البحث عن الموظف (بالاسم أو الكود):</label>
          <input type="text" id="employeeSearchAdd" class="employee-search-input" placeholder="أدخل اسم أو كود الموظف" list="employeeSearchSuggestions" autocomplete="off">
          <datalist id="employeeSearchSuggestions"></datalist>
          <small class="search-hint">اكتب كود الموظف أو جزء من الاسم ثم اختر من القائمة</small>
        </div>
      </div>

      <!-- الصف الأول: كود الموظف، اسم الموظف، الإدارة -->
      <div class="form-row">
        <div class="form-group">
          <label for="employeeCode">كود الموظف:</label>
          <input type="text" id="employeeCode" placeholder="كود الموظف" readonly>
        </div>

        <div class="form-group">
          <label for="employeeName">اسم الموظف:</label>
          <input type="text" id="employeeName" placeholder="اسم الموظف" readonly>
        </div>

        <div class="form-group">
          <label for="employeeDepartment">الإدارة:</label>
          <input type="text" id="employeeDepartment" placeholder="الإدارة" readonly>
        </div>
      </div>

      <!-- الصف الثاني: قيمة السلفة، تاريخ السلفة، سبب السلفة، طريقة السداد -->
      <div class="form-row">
        <div class="form-group">
          <label for="advanceAmount">قيمة السلفة:</label>
          <input type="number" id="advanceAmount" placeholder="قيمة السلفة" min="0" step="0.01" required>
        </div>

        <div class="form-group">
          <label for="advanceDate">تاريخ السلفة:</label>
          <input type="date" id="advanceDate" required>
        </div>

        <div class="form-group">
          <label for="advanceReason">سبب السلفة:</label>
          <input type="text" id="advanceReason" placeholder="سبب السلفة" required>
        </div>

        <div class="form-group">
          <label for="paymentMethod">طريقة السداد:</label>
          <input type="text" id="paymentMethod" placeholder="طريقة السداد" required>
        </div>
      </div>

      <!-- الصف الثالث: الملاحظات -->
      <div class="form-row">
        <div class="form-group full-width">
          <label for="notes">ملاحظات إضافية:</label>
          <textarea id="notes" placeholder="ملاحظات إضافية" rows="3"></textarea>
        </div>
      </div>

      <div class="form-actions">
        <button type="button" id="saveAdvance" class="btn btn-primary">
          <i class="fas fa-save"></i>
          حفظ السلفة
        </button>
        <button type="button" id="resetForm" class="btn btn-secondary">
          <i class="fas fa-undo"></i>
          إعادة تعيين
        </button>
      </div>
    </div>

    <!-- جدول السلف المضافة -->
    <div class="advances-table-container">
      <h3>السلف المسجلة</h3>

      <!-- فلاتر البحث المحددة -->
      <div class="filtered-search-container">
        <div class="search-filters-row">
          <div class="form-group">
            <label for="filterAdvanceEmployeeCode">كود الموظف:</label>
            <input type="text" id="filterAdvanceEmployeeCode" placeholder="أدخل كود الموظف" class="filter-input">
          </div>

          <div class="form-group">
            <label for="filterAdvanceEmployeeName">اسم الموظف:</label>
            <input type="text" id="filterAdvanceEmployeeName" placeholder="أدخل اسم الموظف" class="filter-input">
          </div>

          <div class="form-group">
            <label for="filterAdvanceFromDate">من تاريخ:</label>
            <input type="date" id="filterAdvanceFromDate" class="filter-input">
          </div>

          <div class="form-group">
            <label for="filterAdvanceToDate">إلى تاريخ:</label>
            <input type="date" id="filterAdvanceToDate" class="filter-input">
          </div>
        </div>

        <div class="filter-actions">
          <button id="clearAdvanceFiltersBtn" class="reset-btn">مسح الفلاتر</button>
        </div>
      </div>

      <div class="table-wrapper">
        <table id="advancesTable" class="table table-striped table-hover">
          <thead>
            <tr>
              <th>كود الموظف</th>
              <th>اسم الموظف</th>
              <th>الإدارة</th>
              <th>قيمة السلفة</th>
              <th>تاريخ السلفة</th>
              <th>سبب السلفة</th>
              <th>طريقة السداد</th>
              <th>ملاحظات</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody id="advancesTableBody">
            <!-- سيتم ملء البيانات هنا بواسطة JavaScript -->
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- محتوى تقارير السلف -->
  <div class="tab-content" id="reports">
    <div class="container-fluid">
      <div class="page-header">
        <h2><i class="fas fa-chart-bar"></i> تقارير السلف</h2>
      </div>
      
      <!-- فلاتر التقارير -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="fas fa-filter"></i>
            فلاتر التقارير
          </h5>
        </div>
        <div class="card-body">
          <div class="form-row">
            <div class="form-group col-md-3">
              <label for="filterStartDate">من تاريخ:</label>
              <input type="date" id="filterStartDate" class="form-control">
            </div>

            <div class="form-group col-md-3">
              <label for="filterEndDate">إلى تاريخ:</label>
              <input type="date" id="filterEndDate" class="form-control">
            </div>

            <div class="form-group col-md-3">
              <label for="filterDepartment">الإدارة:</label>
              <select id="filterDepartment" class="form-control">
                <option value="">جميع الإدارات</option>
              </select>
            </div>

            <div class="form-group col-md-3">
              <label for="filterEmployee">الموظف:</label>
              <input type="text" id="filterEmployee" class="form-control" placeholder="البحث عن موظف" list="filterEmployeeSuggestions" autocomplete="off">
              <datalist id="filterEmployeeSuggestions"></datalist>
            </div>
          </div>

          <div class="form-row">
            <div class="col-12">
              <button type="button" id="applyFilters" class="btn btn-primary">
                <i class="fas fa-filter"></i>
                تطبيق الفلاتر
              </button>
              <button type="button" id="clearFilters" class="btn btn-secondary ml-2">
                <i class="fas fa-times"></i>
                مسح الفلاتر
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- إحصائيات التقرير -->
      <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
          <div class="stats-card">
            <div class="stats-card-body">
              <div class="stats-icon">
                <i class="fas fa-list-ol"></i>
              </div>
              <div class="stats-content">
                <h3 class="stats-number" id="totalAdvances">0</h3>
                <p class="stats-label">إجمالي عدد السلف</p>
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
          <div class="stats-card stats-card-success">
            <div class="stats-card-body">
              <div class="stats-icon">
                <i class="fas fa-money-bill-wave"></i>
              </div>
              <div class="stats-content">
                <h3 class="stats-number" id="totalAmount">0</h3>
                <p class="stats-label">إجمالي قيمة السلف</p>
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-6 col-md-12">
          <div class="export-buttons-container">
            <h6 class="export-title">
              <i class="fas fa-download"></i>
              تصدير التقارير
            </h6>
            <div class="export-buttons">
              <button type="button" id="printReport" class="btn btn-outline-success">
                <i class="fas fa-print"></i>
                طباعة التقرير
              </button>
              <button type="button" id="exportExcel" class="btn btn-outline-info">
                <i class="fas fa-file-excel"></i>
                تصدير Excel
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- جدول التقارير -->
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="fas fa-table"></i>
            نتائج التقرير
          </h5>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table id="reportsTable" class="table table-striped table-hover mb-0">
              <thead class="thead-dark">
                <tr>
                  <th>كود الموظف</th>
                  <th>اسم الموظف</th>
                  <th>الإدارة</th>
                  <th>قيمة السلفة</th>
                  <th>تاريخ السلفة</th>
                  <th>سبب السلفة</th>
                  <th>طريقة السداد</th>
                  <th>ملاحظات</th>
                </tr>
              </thead>
              <tbody id="reportsTableBody">
                <!-- سيتم ملء البيانات هنا بواسطة JavaScript -->
              </tbody>
            </table>
          </div>
        </div>

        </div>
      </div>
    </div>
  </div>
</div>

<!-- نافذة تعديل السلفة -->
<div id="editAdvanceModal" class="modal">
  <div class="modal-content">
    <div class="modal-header">
      <h3>تعديل السلفة</h3>
      <span class="close">&times;</span>
    </div>
    <div class="modal-body">
      <!-- البحث عن الموظف -->
      <div class="form-row">
        <div class="form-group full-width">
          <label for="editEmployeeSearch">البحث عن الموظف (بالاسم أو الكود):</label>
          <input type="text" id="editEmployeeSearch" class="employee-search-input" placeholder="أدخل اسم أو كود الموظف" list="editEmployeeSuggestions" autocomplete="off">
          <datalist id="editEmployeeSuggestions"></datalist>
          <small class="search-hint">اكتب كود الموظف أو جزء من الاسم ثم اختر من القائمة</small>
        </div>
      </div>

      <!-- الصف الأول: كود الموظف، اسم الموظف، الإدارة -->
      <div class="form-row">
        <div class="form-group">
          <label for="editEmployeeCode">كود الموظف:</label>
          <input type="text" id="editEmployeeCode" placeholder="كود الموظف" readonly>
        </div>

        <div class="form-group">
          <label for="editEmployeeName">اسم الموظف:</label>
          <input type="text" id="editEmployeeName" placeholder="اسم الموظف" readonly>
        </div>

        <div class="form-group">
          <label for="editEmployeeDepartment">الإدارة:</label>
          <input type="text" id="editEmployeeDepartment" placeholder="الإدارة" readonly>
        </div>
      </div>

      <!-- الصف الثاني: قيمة السلفة، تاريخ السلفة، سبب السلفة، طريقة السداد -->
      <div class="form-row">
        <div class="form-group">
          <label for="editAdvanceAmount">قيمة السلفة:</label>
          <input type="number" id="editAdvanceAmount" placeholder="قيمة السلفة" min="0" step="0.01" required>
        </div>

        <div class="form-group">
          <label for="editAdvanceDate">تاريخ السلفة:</label>
          <input type="date" id="editAdvanceDate" required>
        </div>

        <div class="form-group">
          <label for="editAdvanceReason">سبب السلفة:</label>
          <input type="text" id="editAdvanceReason" placeholder="سبب السلفة" required>
        </div>

        <div class="form-group">
          <label for="editPaymentMethod">طريقة السداد:</label>
          <input type="text" id="editPaymentMethod" placeholder="طريقة السداد" required>
        </div>
      </div>

      <!-- الصف الثالث: الملاحظات -->
      <div class="form-row">
        <div class="form-group full-width">
          <label for="editNotes">ملاحظات إضافية:</label>
          <textarea id="editNotes" placeholder="ملاحظات إضافية" rows="3"></textarea>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button type="button" id="updateAdvance" class="btn btn-primary">
        <i class="fas fa-save"></i>
        حفظ التغييرات
      </button>
      <button type="button" class="btn btn-secondary close-modal">
        <i class="fas fa-times"></i>
        إلغاء
      </button>
    </div>
  </div>
</div>

<!-- تضمين الملفات المطلوبة -->
<script src="shared-utils.js"></script>
<script src="dateUtils.js"></script>
<script src="permissions.js"></script>

<script src="salaryAdvance.js"></script>





</body>
</html>
