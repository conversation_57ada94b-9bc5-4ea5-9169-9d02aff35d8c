<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>إدارة المستخدمين والصلاحيات</title>
  <link rel="stylesheet" href="style.css" />
  <link rel="stylesheet" href="index-table.css">
  <link rel="stylesheet" href="users.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="no-sidebar-layout.css">
  <script src="permissions.js" defer></script>

    <!-- تحميل Day.js للتواريخ -->
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/dayjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/plugin/utc.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/plugin/timezone.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/plugin/customParseFormat.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/plugin/localizedFormat.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.13/locale/ar.js"></script>
    
    <!-- تحميل DateFormatter الموحد -->
    <script src="utils/dateFormatter-browser.js"></script>
    
    <script>
        // تهيئة Day.js
        if (typeof dayjs !== 'undefined') {
            dayjs.extend(dayjs_plugin_utc);
            dayjs.extend(dayjs_plugin_timezone);
            dayjs.extend(dayjs_plugin_customParseFormat);
            dayjs.extend(dayjs_plugin_localizedFormat);
            dayjs.locale('ar');
            console.log('DateFormatter تم تحميله بنجاح');
        }
    </script>
</head>
<body class="users-page">

    <!-- زر العودة للوحة التحكم -->
  <div class="back-to-dashboard">
    <a href="dashboard.html" class="dashboard-btn">
      <i class="fas fa-home"></i>
      <span>العودة للوحة التحكم</span>
    </a>
  </div>



  <div class="main-content full-width" id="mainContent">
    <h1>إدارة المستخدمين والصلاحيات</h1>

    <div class="server-controls">
      <button id="serverStatus" class="status-btn">حالة السيرفر: غير متصل</button>
      <button id="toggleServer" class="control-btn">تشغيل السيرفر</button>
    </div>

    <div class="actions-bar">
      <button id="addUserBtn" class="add-user-btn" data-permission="manage_users">إضافة مستخدم جديد</button>
    </div>

    <div class="users-table-container">
      <table class="users-table">
        <thead>
          <tr>
            <th>الرقم</th>
            <th>اسم المستخدم</th>
            <th>تاريخ الإنشاء</th>
            <th>الصلاحيات</th>
            <th>الإجراءات</th>
          </tr>
        </thead>
        <tbody id="usersTableBody"></tbody>
      </table>
    </div>
  </div>

  <!-- Modal for adding/editing user -->
  <div id="userModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2 id="modalTitle">إضافة مستخدم جديد</h2>
        <span class="close">&times;</span>
      </div>
      <div class="modal-body">
        <form id="userForm">
          <input type="hidden" id="userId">
          
          <div class="user-info-section">
            <h3 class="section-title">معلومات المستخدم</h3>
            <div class="form-group">
              <label for="username">اسم المستخدم</label>
              <input type="text" id="username" placeholder="أدخل اسم المستخدم" required>
            </div>
            <div id="passwordGroup">
              <div class="form-group">
                <label for="password">كلمة المرور</label>
                <input type="password" id="password" placeholder="أدخل كلمة المرور" required>
              </div>
              <div class="form-group">
                <label for="confirmPassword">تأكيد كلمة المرور</label>
                <input type="password" id="confirmPassword" placeholder="أعد إدخال كلمة المرور" required>
              </div>
            </div>
          </div>
          
          <h3 class="section-title">صلاحيات المستخدم</h3>

           <div class="permissions-container">
             <!-- قسم الصلاحيات الأساسية -->
             <div class="permission-section">
               <div class="section-header">
                 <h4>🔑 الصلاحيات الأساسية</h4>
                 <div class="section-controls">
                   <button type="button" class="toggle-all-btn" onclick="toggleSectionPermissions('basic')">تحديد الكل</button>
                   <button type="button" class="section-toggle" onclick="toggleSection('basic')">
                     <span class="toggle-icon">▼</span>
                   </button>
                 </div>
               </div>
               <div class="section-content" id="basic-section">
                 <div class="permissions-grid">
                   <div class="permission-group">
                     <h5>الصلاحيات العامة</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="can_view" data-section="basic">
                       <label for="can_view">عرض البيانات</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="can_add" data-section="basic">
                       <label for="can_add">إضافة بيانات</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="can_edit" data-section="basic">
                       <label for="can_edit">تعديل بيانات</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="can_delete" data-section="basic">
                       <label for="can_delete">حذف بيانات</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>صلاحيات الوصول</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="dashboard_access" data-section="basic">
                       <label for="dashboard_access">الوصول للوحة الرئيسية</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="full_access" data-section="basic">
                       <label for="full_access">صلاحيات كاملة</label>
                     </div>
                   </div>
                 </div>
               </div>
             </div>

             <!-- قسم إدارة الموظفين -->
             <div class="permission-section">
               <div class="section-header">
                 <h4>👥 إدارة الموظفين</h4>
                 <div class="section-controls">
                   <button type="button" class="toggle-all-btn" onclick="toggleSectionPermissions('employees')">تحديد الكل</button>
                   <button type="button" class="section-toggle" onclick="toggleSection('employees')">
                     <span class="toggle-icon">▼</span>
                   </button>
                 </div>
               </div>
               <div class="section-content" id="employees-section">
                 <div class="permissions-grid">
                   <div class="permission-group">
                     <h5>الصفحة الرئيسية</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="view_employees" data-section="employees">
                       <label for="view_employees">الوصول لصفحة الموظفين</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>العمليات الأساسية</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="add_employees" data-section="employees">
                       <label for="add_employees">إضافة موظف جديد</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="edit_employees" data-section="employees">
                       <label for="edit_employees">تعديل بيانات الموظف</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="delete_employees" data-section="employees">
                       <label for="delete_employees">حذف موظف</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>العمليات المتقدمة</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="export_employees" data-section="employees">
                       <label for="export_employees">تصدير بيانات الموظفين</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="import_employees" data-section="employees">
                       <label for="import_employees">استيراد بيانات الموظفين</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="bulk_delete_employees" data-section="employees">
                       <label for="bulk_delete_employees">حذف متعدد للموظفين</label>
                     </div>
                   </div>
                 </div>
               </div>
             </div>

             <!-- قسم الإجازات -->
             <div class="permission-section">
               <div class="section-header">
                 <h4>🏖️ إدارة الإجازات</h4>
                 <div class="section-controls">
                   <button type="button" class="toggle-all-btn" onclick="toggleSectionPermissions('vacations')">تحديد الكل</button>
                   <button type="button" class="section-toggle" onclick="toggleSection('vacations')">
                     <span class="toggle-icon">▼</span>
                   </button>
                 </div>
               </div>
               <div class="section-content" id="vacations-section">
                 <div class="permissions-grid">
                   <div class="permission-group">
                     <h5>الصفحة الرئيسية</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="view_vacations" data-section="vacations">
                       <label for="view_vacations">الوصول لصفحة الإجازات</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>إدارة الإجازات</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="add_vacation" data-section="vacations">
                       <label for="add_vacation">إضافة إجازة جديدة</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="edit_vacation" data-section="vacations">
                       <label for="edit_vacation">تعديل الإجازة</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="delete_vacation" data-section="vacations">
                       <label for="delete_vacation">حذف الإجازة</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>التقارير والعرض</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="view_vacations_list" data-section="vacations">
                       <label for="view_vacations_list">عرض قائمة الإجازات</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="view_vacation_reports" data-section="vacations">
                       <label for="view_vacation_reports">تقارير الإجازات</label>
                     </div>
                   </div>
                 </div>
               </div>
             </div>
             <!-- قسم المساهمات -->
             <div class="permission-section">
               <div class="section-header">
                 <h4>💰 إدارة المساهمات</h4>
                 <div class="section-controls">
                   <button type="button" class="toggle-all-btn" onclick="toggleSectionPermissions('contributions')">تحديد الكل</button>
                   <button type="button" class="section-toggle" onclick="toggleSection('contributions')">
                     <span class="toggle-icon">▼</span>
                   </button>
                 </div>
               </div>
               <div class="section-content" id="contributions-section">
                 <div class="permissions-grid">
                   <div class="permission-group">
                     <h5>الصفحة الرئيسية</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="view_contributions" data-section="contributions">
                       <label for="view_contributions">الوصول لصفحة المساهمات</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>إدارة المساهمات</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="add_contribution" data-section="contributions">
                       <label for="add_contribution">إضافة مساهمة جديدة</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="edit_contribution" data-section="contributions">
                       <label for="edit_contribution">تعديل المساهمة</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="delete_contribution" data-section="contributions">
                       <label for="delete_contribution">حذف المساهمة</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>التقارير والعرض</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="view_contributions_list" data-section="contributions">
                       <label for="view_contributions_list">عرض قائمة المساهمات</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="view_contributions_reports" data-section="contributions">
                       <label for="view_contributions_reports">تقارير المساهمات</label>
                     </div>
                   </div>
                 </div>
               </div>
             </div>

             <!-- قسم المكافآت والخصومات -->
             <div class="permission-section">
               <div class="section-header">
                 <h4>🎁 المكافآت والخصومات</h4>
                 <div class="section-controls">
                   <button type="button" class="toggle-all-btn" onclick="toggleSectionPermissions('rewards')">تحديد الكل</button>
                   <button type="button" class="section-toggle" onclick="toggleSection('rewards')">
                     <span class="toggle-icon">▼</span>
                   </button>
                 </div>
               </div>
               <div class="section-content" id="rewards-section">
                 <div class="permissions-grid">
                   <div class="permission-group">
                     <h5>الصفحة الرئيسية</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="view_rewards_deductions" data-section="rewards">
                       <label for="view_rewards_deductions">الوصول لصفحة المكافآت والخصومات</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>المكافآت</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="rewards_access" data-section="rewards">
                       <label for="rewards_access">إدارة المكافآت</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="add_reward" data-section="rewards">
                       <label for="add_reward">إضافة مكافأة</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="edit_reward" data-section="rewards">
                       <label for="edit_reward">تعديل المكافأة</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="delete_reward" data-section="rewards">
                       <label for="delete_reward">حذف المكافأة</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>الخصومات</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="deductions_access" data-section="rewards">
                       <label for="deductions_access">إدارة الخصومات</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="add_deduction" data-section="rewards">
                       <label for="add_deduction">إضافة خصم</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="edit_deduction" data-section="rewards">
                       <label for="edit_deduction">تعديل الخصم</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="delete_deduction" data-section="rewards">
                       <label for="delete_deduction">حذف الخصم</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>التقارير</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="rewards_reports" data-section="rewards">
                       <label for="rewards_reports">تقارير المكافآت والخصومات</label>
                     </div>
                   </div>
                 </div>
               </div>
             </div>

             <!-- قسم العهد -->
             <div class="permission-section">
               <div class="section-header">
                 <h4>📋 إدارة العهد</h4>
                 <div class="section-controls">
                   <button type="button" class="toggle-all-btn" onclick="toggleSectionPermissions('custody')">تحديد الكل</button>
                   <button type="button" class="section-toggle" onclick="toggleSection('custody')">
                     <span class="toggle-icon">▼</span>
                   </button>
                 </div>
               </div>
               <div class="section-content" id="custody-section">
                 <div class="permissions-grid">
                   <div class="permission-group">
                     <h5>الصفحة الرئيسية</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="view_custody" data-section="custody">
                       <label for="view_custody">الوصول لصفحة العهد</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>إدارة العهد</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="add_custody" data-section="custody">
                       <label for="add_custody">إضافة عهدة جديدة</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="edit_custody" data-section="custody">
                       <label for="edit_custody">تعديل العهدة</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="delete_custody" data-section="custody">
                       <label for="delete_custody">حذف العهدة</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="deliver_custody" data-section="custody">
                       <label for="deliver_custody">تسليم العهد</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="edit_deliver_custody" data-section="custody">
                       <label for="edit_deliver_custody">تعديل تسليم العهد</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="delete_deliver_custody" data-section="custody">
                       <label for="delete_deliver_custody">حذف تسليم العهد</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="return_custody" data-section="custody">
                       <label for="return_custody">استرجاع العهدة</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="edit_return_custody" data-section="custody">
                       <label for="edit_return_custody">تعديل استرجاع العهدة</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="delete_return_custody" data-section="custody">
                       <label for="delete_return_custody">حذف استرجاع العهدة</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>التقارير والعرض</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="view_undelivered_custody" data-section="custody">
                       <label for="view_undelivered_custody">العهد غير المسلمة</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="view_employee_custody" data-section="custody">
                       <label for="view_employee_custody">عهدة الموظف</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="custody_reports" data-section="custody">
                       <label for="custody_reports">تقارير العهد</label>
                     </div>
                   </div>
                 </div>
               </div>
             </div>

             <!-- قسم التقييم -->
             <div class="permission-section">
               <div class="section-header">
                 <h4>⭐ إدارة التقييم</h4>
                 <div class="section-controls">
                   <button type="button" class="toggle-all-btn" onclick="toggleSectionPermissions('evaluation')">تحديد الكل</button>
                   <button type="button" class="section-toggle" onclick="toggleSection('evaluation')">
                     <span class="toggle-icon">▼</span>
                   </button>
                 </div>
               </div>
               <div class="section-content" id="evaluation-section">
                 <div class="permissions-grid">
                   <div class="permission-group">
                     <h5>الصفحة الرئيسية</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="view_evaluation" data-section="evaluation">
                       <label for="view_evaluation">الوصول لصفحة التقييم</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>إدارة التقييم</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="add_evaluation" data-section="evaluation">
                       <label for="add_evaluation">إضافة تقييم جديد</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="edit_evaluation" data-section="evaluation">
                       <label for="edit_evaluation">تعديل التقييم</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="delete_evaluation" data-section="evaluation">
                       <label for="delete_evaluation">حذف التقييم</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>التقارير والإحصائيات</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="view_evaluation_reports" data-section="evaluation">
                       <label for="view_evaluation_reports">تقارير التقييم</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="view_top_bottom_evaluations" data-section="evaluation">
                       <label for="view_top_bottom_evaluations">أعلى وأقل التقييمات</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="view_unevaluated_employees" data-section="evaluation">
                       <label for="view_unevaluated_employees">الموظفين غير المقيمين</label>
                     </div>
                   </div>
                 </div>
               </div>
             </div>

             <!-- قسم العامل المثالي -->
             <div class="permission-section">
               <div class="section-header">
                 <h4>🏆 إدارة العامل المثالي</h4>
                 <div class="section-controls">
                   <button type="button" class="toggle-all-btn" onclick="toggleSectionPermissions('ideal-employee')">تحديد الكل</button>
                   <button type="button" class="section-toggle" onclick="toggleSection('ideal-employee')">
                     <span class="toggle-icon">▼</span>
                   </button>
                 </div>
               </div>
               <div class="section-content" id="ideal-employee-section">
                 <div class="permissions-grid">
                   <div class="permission-group">
                     <h5>الصفحة الرئيسية</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="view_ideal_employee" data-section="ideal-employee">
                       <label for="view_ideal_employee">الوصول لصفحة العامل المثالي</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>إدارة العامل المثالي</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="add_ideal_employee" data-section="ideal-employee">
                       <label for="add_ideal_employee">إضافة عامل مثالي</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="edit_ideal_employee" data-section="ideal-employee">
                       <label for="edit_ideal_employee">تعديل العامل المثالي</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="delete_ideal_employee" data-section="ideal-employee">
                       <label for="delete_ideal_employee">حذف العامل المثالي</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>التقارير والإحصائيات</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="view_ideal_employee_reports" data-section="ideal-employee">
                       <label for="view_ideal_employee_reports">تقارير العامل المثالي</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="export_ideal_employee_data" data-section="ideal-employee">
                       <label for="export_ideal_employee_data">تصدير بيانات العامل المثالي</label>
                     </div>
                   </div>
                 </div>
               </div>
             </div>

             <!-- قسم التدريب -->
             <div class="permission-section">
               <div class="section-header">
                 <h4>🎓 إدارة التدريب</h4>
                 <div class="section-controls">
                   <button type="button" class="toggle-all-btn" onclick="toggleSectionPermissions('training')">تحديد الكل</button>
                   <button type="button" class="section-toggle" onclick="toggleSection('training')">
                     <span class="toggle-icon">▼</span>
                   </button>
                 </div>
               </div>
               <div class="section-content" id="training-section">
                 <div class="permissions-grid">
                   <div class="permission-group">
                     <h5>الصفحة الرئيسية</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="view_training" data-section="training">
                       <label for="view_training">الوصول لصفحة التدريب</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>إدارة الدورات التدريبية</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="add_training" data-section="training">
                       <label for="add_training">إضافة دورة تدريبية</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="edit_training" data-section="training">
                       <label for="edit_training">تعديل الدورات التدريبية</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="delete_training" data-section="training">
                       <label for="delete_training">حذف الدورات التدريبية</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>التقارير</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="view_training_reports" data-section="training">
                       <label for="view_training_reports">تقارير التدريب</label>
                     </div>
                   </div>
                 </div>
               </div>
             </div>

             <!-- قسم السلف -->
             <div class="permission-section">
               <div class="section-header">
                 <h4>💳 إدارة السلف</h4>
                 <div class="section-controls">
                   <button type="button" class="toggle-all-btn" onclick="toggleSectionPermissions('advances')">تحديد الكل</button>
                   <button type="button" class="section-toggle" onclick="toggleSection('advances')">
                     <span class="toggle-icon">▼</span>
                   </button>
                 </div>
               </div>
               <div class="section-content" id="advances-section">
                 <div class="permissions-grid">
                   <div class="permission-group">
                     <h5>الصفحة الرئيسية</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="view_salary_advances" data-section="advances">
                       <label for="view_salary_advances">الوصول لصفحة السلف</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>إدارة السلف</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="add_salary_advance" data-section="advances">
                       <label for="add_salary_advance">إضافة سلفة</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="edit_salary_advance" data-section="advances">
                       <label for="edit_salary_advance">تعديل السلف</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="delete_salary_advance" data-section="advances">
                       <label for="delete_salary_advance">حذف السلف</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>التقارير</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="view_salary_advance_reports" data-section="advances">
                       <label for="view_salary_advance_reports">تقارير السلف</label>
                     </div>
                   </div>
                 </div>
               </div>
             </div>

             <!-- قسم الإضافي -->
             <div class="permission-section">
               <div class="section-header">
                 <h4>⏰ إدارة الإضافي</h4>
                 <div class="section-controls">
                   <button type="button" class="toggle-all-btn" onclick="toggleSectionPermissions('overtime')">تحديد الكل</button>
                   <button type="button" class="section-toggle" onclick="toggleSection('overtime')">
                     <span class="toggle-icon">▼</span>
                   </button>
                 </div>
               </div>
               <div class="section-content" id="overtime-section">
                 <div class="permissions-grid">
                   <div class="permission-group">
                     <h5>الصفحة الرئيسية</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="view_extra_hours" data-section="overtime">
                       <label for="view_extra_hours">الوصول لصفحة الإضافي</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>إدارة الإضافي</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="add_extra_hour" data-section="overtime">
                       <label for="add_extra_hour">إضافة عمل إضافي</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="edit_extra_hour" data-section="overtime">
                       <label for="edit_extra_hour">تعديل الإضافي</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="delete_extra_hour" data-section="overtime">
                       <label for="delete_extra_hour">حذف الإضافي</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>التقارير</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="view_extra_hour_reports" data-section="overtime">
                       <label for="view_extra_hour_reports">تقارير الإضافي</label>
                     </div>
                   </div>
                 </div>
               </div>
             </div>

             <!-- قسم الاستقالات -->
             <div class="permission-section">
               <div class="section-header">
                 <h4>📤 إدارة الاستقالات</h4>
                 <div class="section-controls">
                   <button type="button" class="toggle-all-btn" onclick="toggleSectionPermissions('resignations')">تحديد الكل</button>
                   <button type="button" class="section-toggle" onclick="toggleSection('resignations')">
                     <span class="toggle-icon">▼</span>
                   </button>
                 </div>
               </div>
               <div class="section-content" id="resignations-section">
                 <div class="permissions-grid">
                   <div class="permission-group">
                     <h5>الصفحة الرئيسية</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="view_resignations" data-section="resignations">
                       <label for="view_resignations">الوصول لصفحة الاستقالات</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>إدارة الاستقالات</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="add_resignation" data-section="resignations">
                       <label for="add_resignation">إضافة استقالة</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="edit_resignation" data-section="resignations">
                       <label for="edit_resignation">تعديل الاستقالات</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="delete_resignation" data-section="resignations">
                       <label for="delete_resignation">حذف الاستقالات</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>التقارير</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="view_resignation_reports" data-section="resignations">
                       <label for="view_resignation_reports">تقارير الاستقالات</label>
                     </div>
                   </div>
                 </div>
               </div>
             </div>

             <!-- قسم إدارة النظام -->
             <div class="permission-section">
               <div class="section-header">
                 <h4>⚙️ إدارة النظام</h4>
                 <div class="section-controls">
                   <button type="button" class="toggle-all-btn" onclick="toggleSectionPermissions('system')">تحديد الكل</button>
                   <button type="button" class="section-toggle" onclick="toggleSection('system')">
                     <span class="toggle-icon">▼</span>
                   </button>
                 </div>
               </div>
               <div class="section-content" id="system-section">
                 <div class="permissions-grid">
                   <div class="permission-group">
                     <h5>إدارة البيانات</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="view_import" data-section="system">
                       <label for="view_import">استيراد بيانات</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="export_data" data-section="system">
                       <label for="export_data">تصدير البيانات</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="backup_data" data-section="system">
                       <label for="backup_data">نسخ احتياطي</label>
                     </div>
                   </div>
                   <div class="permission-group">
                     <h5>إدارة المستخدمين</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="manage_users" data-section="system">
                       <label for="manage_users">إدارة المستخدمين</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="manage_permissions" data-section="system">
                       <label for="manage_permissions">إدارة الصلاحيات</label>
                     </div>
                    <div class="permission-item">
                      <input type="checkbox" id="view_activity_log" data-section="system">
                      <label for="view_activity_log">عرض سجل الأنشطة</label>
                    </div>
                   </div>
                   <div class="permission-group">
                     <h5>عمليات خطيرة</h5>
                     <div class="permission-item">
                       <input type="checkbox" id="delete_all_employees" data-section="system">
                       <label for="delete_all_employees">حذف جميع الموظفين</label>
                     </div>
                     <div class="permission-item">
                       <input type="checkbox" id="reset_system" data-section="system">
                       <label for="reset_system">إعادة تعيين النظام</label>
                     </div>
                   </div>
                 </div>
               </div>
             </div>

             <!-- أزرار التحكم العامة -->
             <div class="permissions-global-actions">
               <button type="button" class="global-action-btn primary" onclick="selectAllPermissions()">تحديد جميع الصلاحيات</button>
               <button type="button" class="global-action-btn secondary" onclick="clearAllPermissions()">إلغاء تحديد الكل</button>
               <button type="button" class="global-action-btn" onclick="selectBasicPermissions()">الصلاحيات الأساسية</button>
               <button type="button" class="global-action-btn" onclick="selectViewOnlyPermissions()">صلاحيات العرض فقط</button>
               <button type="button" class="global-action-btn" onclick="expandAllSections()">توسيع جميع الأقسام</button>
               <button type="button" class="global-action-btn" onclick="collapseAllSections()">طي جميع الأقسام</button>
             </div>
           </div>
        </form>
      </div>
      <div class="modal-footer">
        <button id="saveUserBtn" class="confirm-btn">حفظ</button>
        <button class="cancel-btn">إلغاء</button>
      </div>
    </div>
  </div>

  <script src="users.js"></script>
  

<script>
// دالة تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        // مسح البيانات المحفوظة
        localStorage.removeItem('token');
        localStorage.removeItem('permissions');
        localStorage.removeItem('userInfo');
        localStorage.removeItem('activeSection');
        localStorage.removeItem('userName');
        
        // إعادة التوجيه لصفحة تسجيل الدخول
        window.location.href = 'login.html';
    }
}

// تم حذف دالة updateSidebarUserInfo المتعارضة - يتم التحكم بها من sidebar-standalone.js فقط

// تحميل القائمة الجانبية مع النظام المحسن

    };
    document.head.appendChild(script);
});
</script>
</body>
</html>