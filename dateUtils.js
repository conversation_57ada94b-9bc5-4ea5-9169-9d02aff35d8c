/**
 * مكتبة مركزية للتعامل مع التواريخ - محدثة لاستخدام Day.js
 * تم استبدال جميع الدوال القديمة بدوال موحدة من DateFormatter
 */

// استيراد المكتبة الموحدة الجديدة
const DateFormatter = require('./utils/dateFormatter');

// إعدادات التوافق مع الكود القديم
const EGYPT_TIMEZONE = DateFormatter.EGYPT_TIMEZONE;
const EGYPT_UTC_OFFSET = 2; // للتوافق مع الكود القديم

/**
 * تحويل التاريخ إلى توقيت مصر - محدث لاستخدام DateFormatter
 * @param {string|Date} dateInput - التاريخ المدخل
 * @returns {Date} - التاريخ بتوقيت مصر
 * @deprecated استخدم DateFormatter.formatDate بدلاً من ذلك
 */
function toEgyptTime(dateInput) {
  if (!dateInput) return null;

  try {
    // استخدام DateFormatter للتحقق من صحة التاريخ
    if (!DateFormatter.isValidDate(dateInput)) {
      return null;
    }

    // إرجاع Date object للتوافق مع الكود القديم
    return new Date(dateInput);
  } catch (error) {
    console.error('خطأ في تحويل التاريخ إلى توقيت مصر:', error);
    return null;
  }
}

/**
 * تنسيق التاريخ للعرض بالصيغة العربية - محدث لاستخدام DateFormatter
 * @param {string|Date} dateInput - التاريخ المدخل
 * @returns {string} - التاريخ منسق
 * @deprecated استخدم DateFormatter.formatDate بدلاً من ذلك
 */
function formatDateArabic(dateInput) {
  return DateFormatter.formatDate(dateInput);
}

/**
 * تنسيق التاريخ للعرض بالصيغة الطويلة باللغة العربية - محدث لاستخدام DateFormatter
 * @param {string|Date} dateInput - التاريخ المدخل
 * @returns {string} - التاريخ منسق بالصيغة الطويلة
 * @deprecated استخدم DateFormatter.formatDateLong بدلاً من ذلك
 */
function formatDateLongArabic(dateInput) {
  return DateFormatter.formatDateLong(dateInput);
}

/**
 * تنسيق التاريخ لحقول الإدخال - محدث لاستخدام DateFormatter
 * @param {string|Date} dateInput - التاريخ المدخل
 * @returns {string} - التاريخ بصيغة YYYY-MM-DD
 * @deprecated استخدم DateFormatter.formatDateForInput بدلاً من ذلك
 */
function formatDateForInput(dateInput) {
  return DateFormatter.formatDateForInput(dateInput);
}

/**
 * تنسيق التاريخ لقاعدة البيانات - محدث لاستخدام DateFormatter
 * @param {string|Date} dateInput - التاريخ المدخل
 * @returns {string|null} - التاريخ بصيغة قاعدة البيانات أو null
 * @deprecated استخدم DateFormatter.formatDateForDatabase بدلاً من ذلك
 */
function formatDateForDatabase(dateInput) {
  return DateFormatter.formatDateForDatabase(dateInput);
}

/**
 * حساب العمر بالسنوات - محدث لاستخدام DateFormatter
 * @param {string|Date} birthDate - تاريخ الميلاد
 * @returns {number|null} - العمر بالسنوات
 * @deprecated استخدم DateFormatter.calculateAge بدلاً من ذلك
 */
function calculateAge(birthDate) {
  return DateFormatter.calculateAge(birthDate);
}

/**
 * حساب مدة الخدمة - محدث لاستخدام DateFormatter
 * @param {string|Date} hireDate - تاريخ التوظيف
 * @returns {string} - مدة الخدمة منسقة
 * @deprecated استخدم DateFormatter.calculateServiceDuration بدلاً من ذلك
 */
function calculateServiceDuration(hireDate) {
  const result = DateFormatter.calculateServiceDuration(hireDate);
  return result ? result.formatted : 'غير محدد';
}

/**
 * التحقق من صحة التاريخ - محدث لاستخدام DateFormatter
 * @param {string|Date} dateInput - التاريخ المدخل
 * @returns {boolean} - true إذا كان التاريخ صحيح
 * @deprecated استخدم DateFormatter.isValidDate بدلاً من ذلك
 */
function isValidDate(dateInput) {
  return DateFormatter.isValidDate(dateInput);
}

/**
 * معالجة خاصة للتواريخ القادمة من قاعدة البيانات - محدث لاستخدام DateFormatter
 * @param {string|Date} dateInput - التاريخ من قاعدة البيانات
 * @returns {string} - التاريخ منسق
 * @deprecated استخدم DateFormatter.formatDateFromDatabase بدلاً من ذلك
 */
function formatDateFromDatabase(dateInput) {
  return DateFormatter.formatDateFromDatabase(dateInput);
}

// تصدير الدوال - محدث للتوافق مع الكود القديم
if (typeof module !== 'undefined' && module.exports) {
  // Node.js environment - تصدير الدوال القديمة للتوافق + الدوال الجديدة
  module.exports = {
    // الدوال القديمة للتوافق
    toEgyptTime,
    formatDateArabic,
    formatDateLongArabic,
    formatDateForInput,
    formatDateForDatabase,
    formatDateFromDatabase,
    calculateAge,
    calculateServiceDuration,
    isValidDate,
    EGYPT_TIMEZONE,
    EGYPT_UTC_OFFSET,

    // الدوال الجديدة من DateFormatter
    ...DateFormatter
  };
} else {
  // Browser environment - للتوافق مع الكود القديم
  window.DateUtils = {
    toEgyptTime,
    formatDateArabic,
    formatDateLongArabic,
    formatDateForInput,
    formatDateForDatabase,
    formatDateFromDatabase,
    calculateAge,
    calculateServiceDuration,
    isValidDate,
    EGYPT_TIMEZONE,
    EGYPT_UTC_OFFSET
  };

  // إضافة DateFormatter للمتصفح إذا لم يكن موجوداً
  if (typeof window.DateFormatter === 'undefined') {
    // سيتم تحميله من ملف منفصل
    console.log('DateFormatter سيتم تحميله من ملف منفصل');
  }
}
