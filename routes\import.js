const express = require("express");
const multer = require("multer");
const csv = require("csv-parser");
const fs = require("fs");
const { pool } = require("../config/database");
const { authenticateToken, checkPermission } = require("../middleware/auth");
const { logAction } = require('../activityLogger');

// استيراد مكتبة التواريخ الموحدة الجديدة
const DateFormatter = require("../utils/dateFormatter");

// دالة لتحويل التاريخ من صيغ مختلفة إلى YYYY-MM-DD باستخدام DateFormatter الجديد
const formatDateForMySQL = (dateString) => {
  return DateFormatter.formatDateForDatabase(dateString);
};

// دالة لمعالجة جميع حقول التاريخ في صف البيانات
const processDateFields = (rowData) => {
  // قائمة بجميع حقول التاريخ المحتملة في النظام
  const dateFields = [
    'birth_date',        // تاريخ الميلاد
    'hire_date',         // تاريخ التوظيف
    'insurance_start',   // تاريخ بداية التأمين
    'skill_start',       // تاريخ بداية المهارة
    'skill_end',         // تاريخ انتهاء المهارة
    'vacation_date',     // تاريخ الإجازة
    'start_date',        // تاريخ البداية (عام)
    'end_date',          // تاريخ النهاية (عام)
    'resignation_date',  // تاريخ الاستقالة
    'work_end_date',     // تاريخ ترك العمل
    'delivery_date',     // تاريخ التسليم
    'penalty_date',      // تاريخ الخصم
    'evaluation_date'    // تاريخ التقييم
  ];

  const processedData = { ...rowData };
  const dateErrors = [];

  // معالجة كل حقل تاريخ
  dateFields.forEach(field => {
    if (processedData[field] && processedData[field].toString().trim() !== '') {
      const originalValue = processedData[field].toString().trim();
      const formattedDate = formatDateForMySQL(originalValue);

      if (formattedDate === null) {
        dateErrors.push(`حقل التاريخ "${field}" يحتوي على قيمة غير صالحة: "${originalValue}"`);
      } else {
        // تسجيل التحويل الناجح في وحدة التحكم للمراجعة
        if (originalValue !== formattedDate) {
          console.log(`تم تحويل التاريخ في حقل "${field}" من "${originalValue}" إلى "${formattedDate}"`);
        }
      }

      processedData[field] = formattedDate;
    }
  });

  return { processedData, dateErrors };
};

const router = express.Router();

// إعداد multer لرفع الملفات
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = 'uploads/';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({ 
  storage: storage,
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'text/csv' || file.originalname.endsWith('.csv')) {
      cb(null, true);
    } else {
      cb(new Error('يُسمح فقط بملفات CSV'), false);
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  }
});

// استيراد بيانات الموظفين من ملف CSV
router.post('/import', authenticateToken, checkPermission('view_import'), upload.single('csvFile'), async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ error: 'لم يتم رفع أي ملف' });
  }

  const results = [];
  const errors = [];
  let processedCount = 0;
  let successCount = 0;

  try {
    // قراءة ملف CSV
    fs.createReadStream(req.file.path)
      .pipe(csv())
      .on('data', (data) => {
        results.push(data);
      })
      .on('end', async () => {
        try {
          // معالجة البيانات وإدراجها في قاعدة البيانات
          for (const row of results) {
            processedCount++;

            try {
              // التحقق من وجود الحقول المطلوبة
              if (!row.code || !row.full_name) {
                errors.push({
                  row: processedCount,
                  error: 'كود الموظف والاسم مطلوبان',
                  data: row
                });
                continue;
              }

              // معالجة حقول التاريخ
              const { processedData, dateErrors } = processDateFields(row);

              // إذا كان هناك أخطاء في التواريخ، تسجيلها والانتقال للصف التالي
              if (dateErrors.length > 0) {
                errors.push({
                  row: processedCount,
                  error: `أخطاء في التواريخ: ${dateErrors.join(', ')}`,
                  data: row
                });
                continue;
              }

              // التحقق من عدم وجود موظف بنفس الكود
              const [existingEmployee] = await pool.promise().query(
                "SELECT code FROM employees WHERE code = ?",
                [processedData.code]
              );

              if (existingEmployee.length > 0) {
                errors.push({
                  row: processedCount,
                  error: 'كود الموظف موجود بالفعل',
                  data: row
                });
                continue;
              }

              // إدراج الموظف الجديد باستخدام البيانات المعالجة
              await pool.promise().query(
                `INSERT INTO employees (
                  code, full_name, department, job_title, hire_date, address, qualification,
                  phone, birth_date, marital_status, children, national_id, social_insurance,
                  insurance_number, insurance_entity, insurance_start, insurance_job,
                  insurance_salary, worker_cost, company_cost, total_salary, health_card,
                  skill_level, skill_start, skill_end, skill_remaining, skill_job,
                  leave_balance, leave_used, leave_remaining, special_needs, photo, documents
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                  processedData.code || null,
                  processedData.full_name || null,
                  processedData.department || null,
                  processedData.job_title || null,
                  processedData.hire_date || null,
                  processedData.address || null,
                  processedData.qualification || null,
                  processedData.phone || null,
                  processedData.birth_date || null,
                  processedData.marital_status || null,
                  processedData.children || null,
                  processedData.national_id || null,
                  processedData.social_insurance || null,
                  processedData.insurance_number || null,
                  processedData.insurance_entity || null,
                  processedData.insurance_start || null,
                  processedData.insurance_job || null,
                  processedData.insurance_salary || null,
                  processedData.worker_cost || null,
                  processedData.company_cost || null,
                  processedData.total_salary || null,
                  processedData.health_card || null,
                  processedData.skill_level || null,
                  processedData.skill_start || null,
                  processedData.skill_end || null,
                  processedData.skill_remaining || null,
                  processedData.skill_job || null,
                  processedData.leave_balance || 0,
                  processedData.leave_used || null,
                  processedData.leave_remaining || null,
                  processedData.special_needs || null,
                  processedData.photo || null,
                  processedData.documents || null
                ]
              );

              successCount++;
            } catch (error) {
              console.error(`خطأ في معالجة الصف ${processedCount}:`, error);
              errors.push({
                row: processedCount,
                error: error.message,
                data: row
              });
            }
          }

          // حذف الملف المؤقت
          fs.unlinkSync(req.file.path);

          // تسجيل النشاط
          await logAction({
            user_id: req.user?.id || null,
            username: req.user?.username || 'مجهول',
            action_type: 'import',
            module: 'employees',
            record_id: null,
            message: `تم استيراد بيانات الموظفين من ملف CSV - إجمالي الصفوف: ${processedCount} - نجح: ${successCount} - فشل: ${errors.length}`
          });

          res.json({
            message: 'تم الانتهاء من عملية الاستيراد',
            total_rows: processedCount,
            successful_imports: successCount,
            failed_imports: errors.length,
            errors: errors
          });
        } catch (error) {
          console.error('خطأ في معالجة ملف CSV:', error);
          
          // حذف الملف المؤقت في حالة الخطأ
          if (fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
          }
          
          res.status(500).json({ error: 'فشل في معالجة ملف CSV' });
        }
      })
      .on('error', (error) => {
        console.error('خطأ في قراءة ملف CSV:', error);
        
        // حذف الملف المؤقت في حالة الخطأ
        if (fs.existsSync(req.file.path)) {
          fs.unlinkSync(req.file.path);
        }
        
        res.status(500).json({ error: 'فشل في قراءة ملف CSV' });
      });
  } catch (error) {
    console.error('خطأ في استيراد ملف CSV:', error);
    
    // حذف الملف المؤقت في حالة الخطأ
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    
    res.status(500).json({ error: 'فشل في استيراد ملف CSV' });
  }
});

// استيراد بيانات الموظفين من Excel (JSON)
router.post('/import-excel', authenticateToken, checkPermission('view_import'), async (req, res) => {
  try {
    const { employees } = req.body;
    
    if (!employees || !Array.isArray(employees)) {
      return res.status(400).json({ error: 'يجب توفير مصفوفة من بيانات الموظفين' });
    }

    const errors = [];
    let processedCount = 0;
    let successCount = 0;

    for (const employee of employees) {
      processedCount++;

      try {
        // التحقق من وجود الحقول المطلوبة
        if (!employee.code || !employee.full_name) {
          errors.push({
            row: processedCount,
            error: 'كود الموظف والاسم مطلوبان',
            data: employee
          });
          continue;
        }

        // معالجة حقول التاريخ
        const { processedData, dateErrors } = processDateFields(employee);

        // إذا كان هناك أخطاء في التواريخ، تسجيلها والانتقال للصف التالي
        if (dateErrors.length > 0) {
          errors.push({
            row: processedCount,
            error: `أخطاء في التواريخ: ${dateErrors.join(', ')}`,
            data: employee
          });
          continue;
        }

        // التحقق من عدم وجود موظف بنفس الكود
        const [existingEmployee] = await pool.promise().query(
          "SELECT code FROM employees WHERE code = ?",
          [processedData.code]
        );

        if (existingEmployee.length > 0) {
          errors.push({
            row: processedCount,
            error: 'كود الموظف موجود بالفعل',
            data: employee
          });
          continue;
        }

        // إدراج الموظف الجديد باستخدام البيانات المعالجة
        await pool.promise().query(
          `INSERT INTO employees (
            code, full_name, department, job_title, hire_date, address, qualification,
            phone, birth_date, marital_status, children, national_id, social_insurance,
            insurance_number, insurance_entity, insurance_start, insurance_job,
            insurance_salary, worker_cost, company_cost, total_salary, health_card,
            skill_level, skill_start, skill_end, skill_remaining, skill_job,
            leave_balance, leave_used, leave_remaining, special_needs, photo, documents
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            processedData.code || null,
            processedData.full_name || null,
            processedData.department || null,
            processedData.job_title || null,
            processedData.hire_date || null,
            processedData.address || null,
            processedData.qualification || null,
            processedData.phone || null,
            processedData.birth_date || null,
            processedData.marital_status || null,
            processedData.children || null,
            processedData.national_id || null,
            processedData.social_insurance || null,
            processedData.insurance_number || null,
            processedData.insurance_entity || null,
            processedData.insurance_start || null,
            processedData.insurance_job || null,
            processedData.insurance_salary || null,
            processedData.worker_cost || null,
            processedData.company_cost || null,
            processedData.total_salary || null,
            processedData.health_card || null,
            processedData.skill_level || null,
            processedData.skill_start || null,
            processedData.skill_end || null,
            processedData.skill_remaining || null,
            processedData.skill_job || null,
            processedData.leave_balance || 0,
            processedData.leave_used || null,
            processedData.leave_remaining || null,
            processedData.special_needs || null,
            processedData.photo || null,
            processedData.documents || null
          ]
        );

        successCount++;
      } catch (error) {
        console.error(`خطأ في معالجة الموظف ${processedCount}:`, error);
        errors.push({
          row: processedCount,
          error: error.message,
          data: employee
        });
      }
    }

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'import',
      module: 'employees',
      record_id: null,
      message: `تم استيراد بيانات الموظفين من ملف Excel - إجمالي الصفوف: ${processedCount} - نجح: ${successCount} - فشل: ${errors.length}`
    });

    res.json({
      message: 'تم الانتهاء من عملية الاستيراد',
      total_rows: processedCount,
      successful_imports: successCount,
      failed_imports: errors.length,
      errors: errors
    });
  } catch (error) {
    console.error('خطأ في استيراد بيانات Excel:', error);
    res.status(500).json({ error: 'فشل في استيراد بيانات Excel' });
  }
});

// الحصول على نموذج CSV للتحميل
router.get('/csv-template', authenticateToken, checkPermission('view_import'), (req, res) => {
  const csvTemplate = `code,full_name,department,job_title,hire_date,address,qualification,phone,birth_date,marital_status,children,national_id,social_insurance,insurance_number,insurance_entity,insurance_start,insurance_job,insurance_salary,worker_cost,company_cost,total_salary,health_card,skill_level,skill_start,skill_end,skill_remaining,skill_job,leave_balance,leave_used,leave_remaining,special_needs,photo,documents
001,أحمد محمد علي,تقنية المعلومات,مطور برمجيات,15/01/2023,الرياض - حي النرجس,بكالوريوس حاسب آلي,**********,20/05/1990,متزوج,2,**********,نعم,INS123456,التأمينات الاجتماعية,01/02/2023,مطور,5000,250,500,5500,HC123456,ممتاز,01/03/2023,01/03/2024,365,تطوير التطبيقات,30,5,25,لا يوجد,,
002,سارة أحمد محمد,الموارد البشرية,محاسبة,01/02/2023,جدة - حي الزهراء,بكالوريوس محاسبة,**********,15/08/1992,عزباء,0,**********,نعم,INS654321,التأمينات الاجتماعية,15/02/2023,محاسبة,4500,225,450,4950,HC654321,جيد جداً,15/03/2023,15/03/2024,365,المحاسبة المالية,30,3,27,لا يوجد,,`;

  res.setHeader('Content-Type', 'text/csv; charset=utf-8');
  res.setHeader('Content-Disposition', 'attachment; filename="employee_template.csv"');
  res.send('\uFEFF' + csvTemplate); // إضافة BOM للدعم العربي
});

// الحصول على إحصائيات الاستيراد
router.get('/import-history', authenticateToken, checkPermission('view_import'), async (req, res) => {
  try {
    // يمكن إضافة جدول لتتبع تاريخ الاستيراد في المستقبل
    // حاليًا نعرض إحصائيات بسيطة
    const [employeeCount] = await pool.promise().query(
      "SELECT COUNT(*) as total_employees FROM employees"
    );
    
    const [recentEmployees] = await pool.promise().query(
      "SELECT employee_code, name, department, hire_date FROM employees ORDER BY id DESC LIMIT 10"
    );
    
    res.json({
      total_employees: employeeCount[0].total_employees,
      recent_employees: recentEmployees,
      message: 'إحصائيات الاستيراد'
    });
  } catch (error) {
    console.error('خطأ في جلب إحصائيات الاستيراد:', error);
    res.status(500).json({ error: 'فشل في جلب إحصائيات الاستيراد' });
  }
});

module.exports = router;