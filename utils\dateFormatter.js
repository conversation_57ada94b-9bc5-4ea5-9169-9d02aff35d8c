/**
 * مكتبة موحدة لتنسيق التواريخ باستخدام Day.js
 * تدعم اللغة العربية وتوفر تنسيق موحد لجميع أجزاء النظام
 */

const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');
const customParseFormat = require('dayjs/plugin/customParseFormat');
const localizedFormat = require('dayjs/plugin/localizedFormat');

// تحميل اللغة العربية
require('dayjs/locale/ar');

// تفعيل الإضافات
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(customParseFormat);
dayjs.extend(localizedFormat);

// تعيين اللغة العربية كافتراضية
dayjs.locale('ar');

// إعدادات التوقيت
const EGYPT_TIMEZONE = 'Africa/Cairo';
const DEFAULT_DATE_FORMAT = 'DD/MM/YYYY';
const DATABASE_DATE_FORMAT = 'YYYY-MM-DD';
const INPUT_DATE_FORMAT = 'YYYY-MM-DD';

/**
 * تنسيق التاريخ للعرض بالصيغة العربية الموحدة
 * @param {string|Date} dateInput - التاريخ المدخل
 * @param {string} format - صيغة التنسيق (اختيارية)
 * @returns {string} - التاريخ منسق
 */
function formatDate(dateInput, format = DEFAULT_DATE_FORMAT) {
  if (!dateInput) return 'غير محدد';
  
  try {
    let date = dayjs(dateInput);
    
    // التحقق من صحة التاريخ
    if (!date.isValid()) {
      return 'تاريخ غير صحيح';
    }
    
    // تحويل إلى توقيت مصر
    date = date.tz(EGYPT_TIMEZONE);
    
    return date.format(format);
  } catch (error) {
    console.error('خطأ في تنسيق التاريخ:', error);
    return 'تاريخ غير صحيح';
  }
}

/**
 * تنسيق التاريخ للعرض بالصيغة الطويلة باللغة العربية
 * @param {string|Date} dateInput - التاريخ المدخل
 * @returns {string} - التاريخ منسق بالصيغة الطويلة
 */
function formatDateLong(dateInput) {
  if (!dateInput) return 'غير محدد';
  
  try {
    let date = dayjs(dateInput);
    
    if (!date.isValid()) {
      return 'تاريخ غير صحيح';
    }
    
    date = date.tz(EGYPT_TIMEZONE);
    
    // تنسيق طويل باللغة العربية
    return date.format('DD MMMM YYYY');
  } catch (error) {
    console.error('خطأ في تنسيق التاريخ الطويل:', error);
    return 'تاريخ غير صحيح';
  }
}

/**
 * تنسيق التاريخ لحقول الإدخال (input type="date")
 * @param {string|Date} dateInput - التاريخ المدخل
 * @returns {string} - التاريخ بصيغة YYYY-MM-DD
 */
function formatDateForInput(dateInput) {
  if (!dateInput) return '';
  
  try {
    let date = dayjs(dateInput);
    
    if (!date.isValid()) {
      return '';
    }
    
    date = date.tz(EGYPT_TIMEZONE);
    
    return date.format(INPUT_DATE_FORMAT);
  } catch (error) {
    console.error('خطأ في تنسيق التاريخ للإدخال:', error);
    return '';
  }
}

/**
 * تنسيق التاريخ لقاعدة البيانات
 * @param {string|Date} dateInput - التاريخ المدخل
 * @returns {string} - التاريخ بصيغة YYYY-MM-DD
 */
function formatDateForDatabase(dateInput) {
  if (!dateInput) return null;
  
  try {
    let date = dayjs(dateInput);
    
    if (!date.isValid()) {
      return null;
    }
    
    date = date.tz(EGYPT_TIMEZONE);
    
    return date.format(DATABASE_DATE_FORMAT);
  } catch (error) {
    console.error('خطأ في تنسيق التاريخ لقاعدة البيانات:', error);
    return null;
  }
}

/**
 * تنسيق التاريخ من قاعدة البيانات للعرض
 * @param {string|Date} dateInput - التاريخ من قاعدة البيانات
 * @returns {string} - التاريخ منسق للعرض
 */
function formatDateFromDatabase(dateInput) {
  if (!dateInput) return 'غير محدد';
  
  try {
    // معالجة خاصة للتواريخ من قاعدة البيانات
    let date;
    
    if (typeof dateInput === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateInput)) {
      // تاريخ بصيغة YYYY-MM-DD
      date = dayjs(dateInput);
    } else if (dateInput instanceof Date) {
      // Date object
      date = dayjs(dateInput);
    } else {
      // أي صيغة أخرى
      date = dayjs(dateInput);
    }
    
    if (!date.isValid()) {
      return 'تاريخ غير صحيح';
    }
    
    return date.format(DEFAULT_DATE_FORMAT);
  } catch (error) {
    console.error('خطأ في تنسيق التاريخ من قاعدة البيانات:', error);
    return 'تاريخ غير صحيح';
  }
}

/**
 * حساب العمر بالسنوات
 * @param {string|Date} birthDate - تاريخ الميلاد
 * @returns {number|null} - العمر بالسنوات
 */
function calculateAge(birthDate) {
  if (!birthDate) return null;
  
  try {
    const birth = dayjs(birthDate);
    const today = dayjs();
    
    if (!birth.isValid()) return null;
    
    const age = today.diff(birth, 'year');
    return age >= 0 ? age : null;
  } catch (error) {
    console.error('خطأ في حساب العمر:', error);
    return null;
  }
}

/**
 * حساب مدة الخدمة
 * @param {string|Date} hireDate - تاريخ التوظيف
 * @returns {object|null} - مدة الخدمة (سنوات، شهور، أيام)
 */
function calculateServiceDuration(hireDate) {
  if (!hireDate) return null;
  
  try {
    const hire = dayjs(hireDate);
    const today = dayjs();
    
    if (!hire.isValid()) return null;
    
    const years = today.diff(hire, 'year');
    const months = today.diff(hire.add(years, 'year'), 'month');
    const days = today.diff(hire.add(years, 'year').add(months, 'month'), 'day');
    
    return {
      years: years,
      months: months,
      days: days,
      totalDays: today.diff(hire, 'day'),
      formatted: `${years} سنة، ${months} شهر، ${days} يوم`
    };
  } catch (error) {
    console.error('خطأ في حساب مدة الخدمة:', error);
    return null;
  }
}

/**
 * التحقق من صحة التاريخ
 * @param {string|Date} dateInput - التاريخ المراد التحقق منه
 * @returns {boolean} - true إذا كان التاريخ صحيح
 */
function isValidDate(dateInput) {
  if (!dateInput) return false;
  
  try {
    const date = dayjs(dateInput);
    return date.isValid();
  } catch (error) {
    return false;
  }
}

/**
 * تنسيق التاريخ مع الوقت
 * @param {string|Date} dateInput - التاريخ المدخل
 * @returns {string} - التاريخ والوقت منسق
 */
function formatDateTime(dateInput) {
  if (!dateInput) return 'غير محدد';
  
  try {
    let date = dayjs(dateInput);
    
    if (!date.isValid()) {
      return 'تاريخ غير صحيح';
    }
    
    date = date.tz(EGYPT_TIMEZONE);
    
    return date.format('DD/MM/YYYY - hh:mm A');
  } catch (error) {
    console.error('خطأ في تنسيق التاريخ والوقت:', error);
    return 'تاريخ غير صحيح';
  }
}

// تصدير الدوال
module.exports = {
  formatDate,
  formatDateLong,
  formatDateForInput,
  formatDateForDatabase,
  formatDateFromDatabase,
  formatDateTime,
  calculateAge,
  calculateServiceDuration,
  isValidDate,
  EGYPT_TIMEZONE,
  DEFAULT_DATE_FORMAT,
  DATABASE_DATE_FORMAT,
  INPUT_DATE_FORMAT
};

// للاستخدام في المتصفح
if (typeof window !== 'undefined') {
  window.DateFormatter = module.exports;
}
